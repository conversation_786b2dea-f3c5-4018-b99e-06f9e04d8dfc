#!/usr/bin/env python3
"""
测试6万行vio_summary.log文件的解析性能
"""

import sys
import os
import time

# 添加插件路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_file_info(file_path):
    """显示文件信息"""
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    file_size = os.path.getsize(file_path)
    file_size_mb = file_size / (1024 * 1024)
    
    # 计算行数
    with open(file_path, 'r', encoding='utf-8') as f:
        line_count = sum(1 for _ in f)
    
    print(f"📁 测试文件信息:")
    print(f"   文件路径: {os.path.abspath(file_path)}")
    print(f"   文件大小: {file_size_mb:.2f} MB ({file_size:,} 字节)")
    print(f"   总行数: {line_count:,} 行")
    print(f"   预计违例数: {line_count // 5:,} 条")
    
    return True

def test_standard_parser(file_path):
    """测试标准解析器"""
    print(f"\n🔧 测试标准解析器...")
    
    try:
        from parser import VioLogParser
        
        parser = VioLogParser()
        
        # 进度回调
        progress_count = 0
        def progress_callback(progress, message):
            nonlocal progress_count
            progress_count += 1
            print(f"   进度: {progress:3d}% - {message}")
        
        start_time = time.time()
        violations = parser.parse_log_file(file_path, progress_callback)
        parse_time = time.time() - start_time
        
        print(f"\n✅ 标准解析器结果:")
        print(f"   违例数量: {len(violations):,}")
        print(f"   解析时间: {parse_time:.2f} 秒")
        print(f"   处理速度: {len(violations)/parse_time:.0f} 记录/秒")
        print(f"   进度更新: {progress_count} 次")
        
        return True, parse_time, len(violations)
        
    except Exception as e:
        print(f"❌ 标准解析器失败: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0

def test_high_performance_parser(file_path):
    """测试高性能解析器"""
    print(f"\n🚀 测试高性能解析器...")
    
    try:
        from parser import HighPerformanceVioLogParser
        
        parser = HighPerformanceVioLogParser()
        
        # 进度回调
        progress_count = 0
        def progress_callback(progress, message):
            nonlocal progress_count
            progress_count += 1
            print(f"   进度: {progress:3d}% - {message}")
        
        start_time = time.time()
        violations = parser.parse_log_file_streaming(file_path, progress_callback)
        parse_time = time.time() - start_time
        
        print(f"\n✅ 高性能解析器结果:")
        print(f"   违例数量: {len(violations):,}")
        print(f"   解析时间: {parse_time:.2f} 秒")
        print(f"   处理速度: {len(violations)/parse_time:.0f} 记录/秒")
        print(f"   进度更新: {progress_count} 次")
        
        return True, parse_time, len(violations)
        
    except Exception as e:
        print(f"❌ 高性能解析器失败: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 Timing Violation 6万行文件性能测试")
    print("=" * 60)
    
    # 测试文件路径
    test_file = "test_vio_summary.log"
    
    # 检查文件信息
    if not test_file_info(test_file):
        return
    
    # 测试标准解析器
    std_success, std_time, std_count = test_standard_parser(test_file)
    
    # 测试高性能解析器
    hp_success, hp_time, hp_count = test_high_performance_parser(test_file)
    
    # 性能对比
    print(f"\n" + "=" * 60)
    print("📊 性能对比结果")
    print("=" * 60)
    
    print(f"{'解析器':<15} {'状态':<6} {'时间(秒)':<10} {'违例数':<10} {'速度(记录/秒)':<15}")
    print("-" * 60)
    
    if std_success:
        std_speed = std_count / std_time if std_time > 0 else 0
        status = "✅" if std_time < 30 else "⚠️"
        print(f"{'标准解析器':<15} {status:<6} {std_time:<10.2f} {std_count:<10,} {std_speed:<15.0f}")
    else:
        print(f"{'标准解析器':<15} {'❌':<6} {'失败':<10} {'N/A':<10} {'N/A':<15}")
    
    if hp_success:
        hp_speed = hp_count / hp_time if hp_time > 0 else 0
        status = "✅" if hp_time < 15 else "⚠️"
        print(f"{'高性能解析器':<15} {status:<6} {hp_time:<10.2f} {hp_count:<10,} {hp_speed:<15.0f}")
    else:
        print(f"{'高性能解析器':<15} {'❌':<6} {'失败':<10} {'N/A':<10} {'N/A':<15}")
    
    # 性能评估
    print(f"\n" + "=" * 60)
    print("🎯 性能评估")
    print("=" * 60)
    
    if hp_success and hp_time < 10:
        print("🎉 优秀! 高性能解析器在10秒内完成，GUI不会卡死")
    elif hp_success and hp_time < 20:
        print("👍 良好! 高性能解析器在20秒内完成，用户体验可接受")
    elif hp_success and hp_time < 30:
        print("⚠️  一般! 解析时间较长，可能影响用户体验")
    else:
        print("❌ 需要优化! 解析时间过长，会导致GUI卡死")
    
    # 性能提升计算
    if std_success and hp_success and std_time > 0 and hp_time > 0:
        speedup = std_time / hp_time
        print(f"\n🚀 性能提升: {speedup:.2f}x")
        
        if speedup > 2:
            print("✅ 高性能解析器显著优于标准解析器")
        elif speedup > 1.5:
            print("👍 高性能解析器有明显改善")
        else:
            print("⚠️  性能提升有限，需要进一步优化")
    
    print(f"\n🏁 测试完成!")

if __name__ == "__main__":
    main()
