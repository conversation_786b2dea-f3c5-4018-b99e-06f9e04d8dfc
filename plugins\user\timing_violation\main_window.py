"""
时序违例确认插件主窗口

提供时序违例确认的主要用户界面。
"""

import os
import sys
from typing import List, Dict, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QTableWidget, QTableWidgetItem,
    QFileDialog, QMessageBox, QProgressBar, QGroupBox, QHeaderView,
    QStatusBar, QToolBar, QAction, QCheckBox, QTextEdit, QDialog,
    QDialogButtonBox, QFormLayout, QRadioButton, QButtonGroup, QApplication,
    QAbstractItemView, QScrollBar, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QMetaObject, Q_ARG, QAbstractTableModel, QModelIndex, QVariant
from PyQt5.QtGui import QFont, QColor, QIcon

from plugins.base import NonModalDialog
from .models import ViolationDataModel
from .parser import VioLogParser, AsyncVioLogParser, HighPerformanceVioLogParser, HighPerformanceAsyncParser, CaseInfoParser


class ViolationTableModel(QAbstractTableModel):
    """高性能违例表格数据模型，支持大数据集虚拟滚动"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._data = []
        self._headers = ["NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]
        self._parent_window = parent

    def rowCount(self, parent=QModelIndex()):
        return len(self._data)

    def columnCount(self, parent=QModelIndex()):
        return len(self._headers)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self._headers[section]
        return QVariant()

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or index.row() >= len(self._data):
            return QVariant()

        violation = self._data[index.row()]
        column = index.column()

        if role == Qt.DisplayRole:
            return self._get_display_data(violation, column)
        elif role == Qt.ForegroundRole:
            return self._get_foreground_color(violation)
        elif role == Qt.BackgroundRole and column == 4:  # 状态列
            return self._get_status_background_color(violation)
        elif role == Qt.TextAlignmentRole:
            if column in [0, 2, 4, 5, 6]:  # NUM, 时间, 状态, 确认人, 确认结果
                return Qt.AlignCenter
            return Qt.AlignLeft | Qt.AlignVCenter
        elif role == Qt.ToolTipRole:
            if column == 1:  # 层级路径
                return violation.get('hier', '')
            elif column == 3:  # 检查信息
                return violation.get('check_info', '')

        return QVariant()

    def _get_display_data(self, violation, column):
        """获取显示数据"""
        if column == 0:  # NUM
            return str(violation.get('num', ''))
        elif column == 1:  # 层级路径
            return violation.get('hier', '')
        elif column == 2:  # 时间(ns)
            time_fs = violation.get('time_fs', 0)
            time_ns = time_fs / 1000000 if time_fs else 0
            return f"{time_ns:.3f}"
        elif column == 3:  # 检查信息
            return violation.get('check_info', '')
        elif column == 4:  # 状态
            status = violation.get('status', 'pending')
            return self._get_status_display(status)
        elif column == 5:  # 确认人
            return violation.get('confirmer', '')
        elif column == 6:  # 确认结果
            result = violation.get('result', '')
            return self._get_result_display(result)
        elif column == 7:  # 操作
            status = violation.get('status', 'pending')
            return "确认" if status == 'pending' else "编辑"

        return ""

    def _get_foreground_color(self, violation):
        """获取前景色"""
        status = violation.get('status', 'pending')
        if status in ['confirmed', 'ignored']:
            return QColor(128, 128, 128)  # 灰色
        return QColor(0, 0, 0)  # 黑色

    def _get_status_background_color(self, violation):
        """获取状态列背景色"""
        status = violation.get('status', 'pending')
        if status == 'confirmed':
            return QColor(144, 238, 144)  # 浅绿色
        elif status == 'ignored':
            return QColor(255, 182, 193)  # 浅红色
        else:
            return QColor(255, 255, 224)  # 浅黄色

    def _get_status_display(self, status):
        """获取状态显示文本"""
        status_map = {
            'pending': '待确认',
            'confirmed': '已确认',
            'ignored': '已忽略'
        }
        return status_map.get(status, status)

    def _get_result_display(self, result):
        """获取结果显示文本"""
        result_map = {
            'pass': '通过',
            'issue': '有问题',
            '': ''
        }
        return result_map.get(result, result)

    def update_data(self, violations):
        """更新数据"""
        self.beginResetModel()
        self._data = violations
        self.endResetModel()

    def get_violation_at_row(self, row):
        """获取指定行的违例数据"""
        if 0 <= row < len(self._data):
            return self._data[row]
        return None


class HighPerformanceTableView(QWidget):
    """高性能表格视图，支持大数据集和虚拟滚动"""

    # 信号定义
    cell_double_clicked = pyqtSignal(int, int)  # 行, 列
    action_button_clicked = pyqtSignal(int, str)  # 行, 动作类型

    def __init__(self, parent=None):
        super().__init__(parent)
        self.model = ViolationTableModel(parent)
        self.init_ui()

        # 性能优化参数
        self.visible_rows = 50  # 可见行数
        self.buffer_rows = 10   # 缓冲行数
        self.row_height = 35    # 行高

        # 当前显示范围
        self.first_visible_row = 0
        self.last_visible_row = 0

        # 缓存的按钮
        self.button_cache = {}

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 创建表头
        self.header_widget = self.create_header()
        layout.addWidget(self.header_widget)

        # 创建滚动区域
        self.scroll_area = QFrame()
        self.scroll_area.setFrameStyle(QFrame.Box)
        self.scroll_area.setLineWidth(1)
        layout.addWidget(self.scroll_area)

        # 创建滚动条
        self.v_scrollbar = QScrollBar(Qt.Vertical)
        self.v_scrollbar.valueChanged.connect(self.on_scroll)

        # 布局滚动区域
        scroll_layout = QHBoxLayout(self.scroll_area)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(0)

        # 创建内容区域
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(0)

        scroll_layout.addWidget(self.content_widget)
        scroll_layout.addWidget(self.v_scrollbar)

        # 初始化行容器
        self.row_widgets = []

    def create_header(self):
        """创建表头"""
        header_widget = QWidget()
        header_widget.setFixedHeight(30)
        header_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f8f8;
                border-bottom: 1px solid #d0d0d0;
                font-weight: bold;
                font-family: "Microsoft YaHei";
            }
        """)

        layout = QHBoxLayout(header_widget)
        layout.setContentsMargins(5, 0, 5, 0)
        layout.setSpacing(0)

        # 列宽配置
        column_widths = [60, 300, 100, 200, 80, 100, 80, 100]
        headers = ["NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]

        for i, (header, width) in enumerate(zip(headers, column_widths)):
            label = QLabel(header)
            label.setFixedWidth(width)
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("border-right: 1px solid #d0d0d0; padding: 5px;")
            layout.addWidget(label)

        return header_widget

    def update_data(self, violations):
        """更新数据"""
        self.model.update_data(violations)
        self.update_scrollbar()
        self.refresh_visible_rows()

    def update_scrollbar(self):
        """更新滚动条"""
        total_rows = self.model.rowCount()
        if total_rows <= self.visible_rows:
            self.v_scrollbar.setVisible(False)
            self.first_visible_row = 0
            self.last_visible_row = total_rows - 1
        else:
            self.v_scrollbar.setVisible(True)
            self.v_scrollbar.setRange(0, total_rows - self.visible_rows)
            self.v_scrollbar.setPageStep(self.visible_rows)
            self.v_scrollbar.setSingleStep(1)

    def on_scroll(self, value):
        """滚动事件处理"""
        self.first_visible_row = value
        self.last_visible_row = min(value + self.visible_rows - 1, self.model.rowCount() - 1)
        self.refresh_visible_rows()

    def refresh_visible_rows(self):
        """刷新可见行"""
        # 清除现有行
        for widget in self.row_widgets:
            widget.setParent(None)
        self.row_widgets.clear()

        # 创建可见行
        for row in range(self.first_visible_row, self.last_visible_row + 1):
            if row < self.model.rowCount():
                row_widget = self.create_row_widget(row)
                self.content_layout.addWidget(row_widget)
                self.row_widgets.append(row_widget)

        # 添加弹性空间
        self.content_layout.addStretch()

    def create_row_widget(self, row):
        """创建行控件"""
        violation = self.model.get_violation_at_row(row)
        if not violation:
            return QWidget()

        row_widget = QWidget()
        row_widget.setFixedHeight(self.row_height)

        # 设置行样式
        if row % 2 == 0:
            row_widget.setStyleSheet("background-color: white;")
        else:
            row_widget.setStyleSheet("background-color: #f9f9f9;")

        layout = QHBoxLayout(row_widget)
        layout.setContentsMargins(5, 0, 5, 0)
        layout.setSpacing(0)

        # 列宽配置
        column_widths = [60, 300, 100, 200, 80, 100, 80, 100]

        for col in range(self.model.columnCount()):
            if col == 7:  # 操作列
                button = self.create_action_button(row, violation)
                button.setFixedWidth(column_widths[col])
                layout.addWidget(button)
            else:
                label = self.create_cell_label(row, col, violation, column_widths[col])
                layout.addWidget(label)

        return row_widget

    def create_cell_label(self, row, col, violation, width):
        """创建单元格标签"""
        index = self.model.index(row, col)
        text = self.model.data(index, Qt.DisplayRole)

        label = QLabel(str(text))
        label.setFixedWidth(width)
        label.setStyleSheet("border-right: 1px solid #e0e0e0; padding: 5px;")

        # 设置对齐方式
        if col in [0, 2, 4, 5, 6]:  # NUM, 时间, 状态, 确认人, 确认结果
            label.setAlignment(Qt.AlignCenter)
        else:
            label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        # 设置颜色
        fg_color = self.model.data(index, Qt.ForegroundRole)
        if fg_color:
            label.setStyleSheet(label.styleSheet() + f"color: {fg_color.name()};")

        # 状态列背景色
        if col == 4:
            bg_color = self.model.data(index, Qt.BackgroundRole)
            if bg_color:
                label.setStyleSheet(label.styleSheet() + f"background-color: {bg_color.name()};")

        # 层级路径列支持双击复制
        if col == 1:
            def make_mouse_event_handler(r, c):
                def mouse_event_handler(event):
                    if event.type() == event.MouseButtonDblClick:
                        self.cell_double_clicked.emit(r, c)
                return mouse_event_handler
            label.mouseDoubleClickEvent = make_mouse_event_handler(row, col)

        # 设置工具提示
        tooltip = self.model.data(index, Qt.ToolTipRole)
        if tooltip:
            label.setToolTip(str(tooltip))

        return label

    def create_action_button(self, row, violation):
        """创建操作按钮"""
        status = violation.get('status', 'pending')

        if status == 'pending':
            button = QPushButton("确认")
            button.setStyleSheet("""
                QPushButton {
                    background-color: #4a9eff;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #3d8ced;
                }
            """)
        else:
            button = QPushButton("编辑")
            button.setStyleSheet("""
                QPushButton {
                    color: #808080;
                    background-color: #f0f0f0;
                    border: 1px solid #c0c0c0;
                    border-radius: 4px;
                    padding: 4px 8px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)

        button.clicked.connect(lambda: self.action_button_clicked.emit(row, status))
        return button


class TimingViolationWindow(NonModalDialog):
    """时序违例确认主窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent, "后仿时序违例确认工具")
        self.setWindowTitle("后仿时序违例确认工具")
        self.resize(1400, 900)
        self.setMinimumSize(1200, 700)
        
        # 数据模型
        self.data_model = ViolationDataModel()
        self.parser = VioLogParser()
        self.async_parser = None

        # 当前数据
        self.current_violations = []
        self.current_case_name = ""
        self.current_corner = ""
        self.current_file_path = ""

        # 性能优化标志
        self.use_high_performance_table = False
        self.performance_threshold = 1000  # 超过1000行使用高性能表格
        
        # 初始化UI
        self.init_ui()
        self.apply_runsim_theme()
        
        # 连接信号
        self.connect_signals()
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(5)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)

        # 创建控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)

        # 创建违例列表
        violation_table = self.create_violation_table()
        main_layout.addWidget(violation_table)

        # 创建状态栏
        status_bar = self.create_status_bar()
        main_layout.addWidget(status_bar)

        # 设置布局比例
        main_layout.setStretchFactor(toolbar, 0)
        main_layout.setStretchFactor(control_panel, 0)
        main_layout.setStretchFactor(violation_table, 1)
        main_layout.setStretchFactor(status_bar, 0)
    
    def create_toolbar(self):
        """创建工具栏"""
        # 创建工具栏容器
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(5, 5, 5, 5)
        
        # 选择文件按钮
        self.select_file_btn_toolbar = QPushButton("选择文件")
        self.select_file_btn_toolbar.setToolTip("选择vio_summary.log文件")
        self.select_file_btn_toolbar.clicked.connect(self.select_log_file)
        toolbar_layout.addWidget(self.select_file_btn_toolbar)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setToolTip("刷新违例列表")
        self.refresh_btn.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(self.refresh_btn)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 导出按钮
        self.export_excel_btn = QPushButton("导出Excel")
        self.export_excel_btn.setToolTip("导出为Excel文件")
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        toolbar_layout.addWidget(self.export_excel_btn)

        self.export_csv_btn = QPushButton("导出CSV")
        self.export_csv_btn.setToolTip("导出为CSV文件")
        self.export_csv_btn.clicked.connect(self.export_to_csv)
        toolbar_layout.addWidget(self.export_csv_btn)

        # 分隔符
        toolbar_layout.addWidget(QLabel("|"))

        # 清除历史按钮
        self.clear_btn = QPushButton("清除历史")
        self.clear_btn.setToolTip("清除当前用例的历史数据")
        self.clear_btn.clicked.connect(self.clear_history)
        toolbar_layout.addWidget(self.clear_btn)

        # 历史管理按钮
        self.history_mgmt_btn = QPushButton("历史管理")
        self.history_mgmt_btn.setToolTip("管理历史确认模式")
        self.history_mgmt_btn.clicked.connect(self.show_history_management)
        toolbar_layout.addWidget(self.history_mgmt_btn)

        # 添加弹性空间
        toolbar_layout.addStretch()

        return toolbar_widget
    
    def create_control_panel(self) -> QGroupBox:
        """创建控制面板"""
        group_box = QGroupBox("控制面板")
        layout = QVBoxLayout(group_box)
        
        # 第一行：文件和基本信息
        first_row = QHBoxLayout()
        
        # 文件路径
        first_row.addWidget(QLabel("文件路径:"))
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setPlaceholderText("请选择vio_summary.log文件")
        first_row.addWidget(self.file_path_edit)
        
        self.select_file_btn = QPushButton("选择文件...")
        self.select_file_btn.clicked.connect(self.select_log_file)
        first_row.addWidget(self.select_file_btn)
        
        layout.addLayout(first_row)
        
        # 第二行：用例信息和复位时间
        second_row = QHBoxLayout()
        
        # 用例名称
        second_row.addWidget(QLabel("用例名称:"))
        self.case_name_edit = QLineEdit()
        self.case_name_edit.setPlaceholderText("自动检测")
        second_row.addWidget(self.case_name_edit)
        
        # Corner选择
        second_row.addWidget(QLabel("Corner:"))
        self.corner_combo = QComboBox()
        self.corner_combo.addItem("请选择...")
        self.corner_combo.addItems(CaseInfoParser.get_valid_corners())
        second_row.addWidget(self.corner_combo)
        
        # 复位时间
        second_row.addWidget(QLabel("复位时间(ns):"))
        self.reset_time_edit = QLineEdit()
        self.reset_time_edit.setPlaceholderText("1000")
        self.reset_time_edit.setText("1000")
        second_row.addWidget(self.reset_time_edit)
        
        layout.addLayout(second_row)
        
        # 第三行：进度和操作按钮
        third_row = QHBoxLayout()
        
        # 进度信息
        self.progress_label = QLabel("进度: 已确认 0/0 (0%)")
        third_row.addWidget(self.progress_label)
        
        third_row.addStretch()
        
        # 操作按钮
        self.auto_confirm_btn = QPushButton("自动确认")
        self.auto_confirm_btn.setToolTip("根据复位时间自动确认违例")
        self.auto_confirm_btn.clicked.connect(self.auto_confirm_violations)
        third_row.addWidget(self.auto_confirm_btn)
        
        self.batch_confirm_btn = QPushButton("批量确认")
        self.batch_confirm_btn.setToolTip("批量确认选中的违例")
        self.batch_confirm_btn.clicked.connect(self.batch_confirm_violations)
        third_row.addWidget(self.batch_confirm_btn)
        
        self.confirm_all_btn = QPushButton("全部确认")
        self.confirm_all_btn.setToolTip("确认所有待确认的违例")
        self.confirm_all_btn.clicked.connect(self.confirm_all_violations)
        third_row.addWidget(self.confirm_all_btn)

        self.apply_history_btn = QPushButton("应用历史")
        self.apply_history_btn.setToolTip("应用历史确认记录到匹配的违例")
        self.apply_history_btn.clicked.connect(self.apply_historical_confirmations)
        third_row.addWidget(self.apply_history_btn)
        
        layout.addLayout(third_row)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        return group_box
    
    def create_violation_table(self) -> QGroupBox:
        """创建违例列表表格"""
        group_box = QGroupBox("时序违例列表")
        layout = QVBoxLayout(group_box)

        # 创建性能提示标签
        self.performance_info_label = QLabel("")
        self.performance_info_label.setStyleSheet("color: #666; font-size: 12px; padding: 2px;")
        self.performance_info_label.setVisible(False)
        layout.addWidget(self.performance_info_label)

        # 创建标准表格（用于小数据集）
        self.violation_table = QTableWidget()
        self.violation_table.setColumnCount(8)

        # 设置表头
        headers = ["NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]
        self.violation_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.violation_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.violation_table.setAlternatingRowColors(True)
        self.violation_table.setSortingEnabled(True)

        # 设置行高
        self.violation_table.verticalHeader().setDefaultSectionSize(35)
        self.violation_table.verticalHeader().setMinimumSectionSize(35)

        # 设置列宽
        header = self.violation_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # NUM
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 层级路径
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 时间
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # 检查信息
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # 状态
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # 确认人
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # 确认结果
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # 操作

        # 设置固定列宽
        self.violation_table.setColumnWidth(0, 60)   # NUM
        self.violation_table.setColumnWidth(2, 100)  # 时间
        self.violation_table.setColumnWidth(4, 80)   # 状态
        self.violation_table.setColumnWidth(5, 100)  # 确认人
        self.violation_table.setColumnWidth(6, 80)   # 确认结果
        self.violation_table.setColumnWidth(7, 100)  # 操作

        # 创建高性能表格（用于大数据集）
        self.high_performance_table = HighPerformanceTableView(self)
        self.high_performance_table.setVisible(False)

        # 添加到布局
        layout.addWidget(self.violation_table)
        layout.addWidget(self.high_performance_table)

        return group_box
    
    def create_status_bar(self):
        """创建状态栏"""
        # 创建状态栏容器
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(5, 2, 5, 2)
        
        # 添加状态信息
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)

        # 添加弹性空间
        status_layout.addStretch()

        # 添加统计信息
        self.stats_label = QLabel("总计: 0条违例 | 已确认: 0条 | 待确认: 0条")
        status_layout.addWidget(self.stats_label)

        # 添加时间信息
        self.time_label = QLabel("")
        status_layout.addWidget(self.time_label)
        
        # 定时更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time_display)
        self.timer.start(1000)  # 每秒更新
        self.update_time_display()

        return status_widget

    def on_cell_double_clicked(self, row: int, column: int):
        """处理表格单元格双击事件"""
        # 只处理层级路径列（第1列，索引为1）
        if column == 1:
            item = self.violation_table.item(row, column)
            if item:
                hier_path = item.text()
                if hier_path:
                    # 跨平台剪贴板复制
                    success = self._copy_to_clipboard(hier_path)

                    if success:
                        # 显示成功提示消息
                        self.status_label.setText(f"已复制层级路径: {hier_path[:50]}{'...' if len(hier_path) > 50 else ''}")
                    else:
                        # 显示失败提示消息
                        self.status_label.setText(f"复制失败，请手动选择文本: {hier_path[:30]}...")

                    # 使用QTimer在3秒后恢复原状态文本
                    QTimer.singleShot(3000, self.restore_status_text)

    def _copy_to_clipboard(self, text: str) -> bool:
        """跨平台剪贴板复制功能

        Args:
            text: 要复制的文本

        Returns:
            bool: 复制是否成功
        """
        try:
            import platform
            system = platform.system().lower()

            # 方法1: 使用PyQt5剪贴板（主要方法）
            clipboard = QApplication.clipboard()

            # 在Linux下，尝试设置多种剪贴板模式
            if system == 'linux':
                # 设置主剪贴板（Ctrl+V粘贴）
                clipboard.setText(text, clipboard.Clipboard)
                # 设置选择剪贴板（鼠标中键粘贴）
                clipboard.setText(text, clipboard.Selection)

                # 验证复制是否成功
                clipboard_text = clipboard.text(clipboard.Clipboard)
                selection_text = clipboard.text(clipboard.Selection)

                if clipboard_text == text or selection_text == text:
                    print(f"Linux剪贴板复制成功: {text[:50]}...")
                    return True
                else:
                    print(f"Linux剪贴板复制验证失败")
                    # 尝试备用方法
                    return self._copy_to_clipboard_fallback(text)
            else:
                # Windows和macOS使用标准剪贴板
                clipboard.setText(text, clipboard.Clipboard)

                # 验证复制是否成功
                clipboard_text = clipboard.text(clipboard.Clipboard)
                if clipboard_text == text:
                    print(f"{system.title()}剪贴板复制成功: {text[:50]}...")
                    return True
                else:
                    print(f"{system.title()}剪贴板复制验证失败")
                    return False

        except Exception as e:
            print(f"剪贴板复制异常: {str(e)}")
            # 尝试备用方法
            return self._copy_to_clipboard_fallback(text)

    def _copy_to_clipboard_fallback(self, text: str) -> bool:
        """备用剪贴板复制方法（使用系统命令）

        Args:
            text: 要复制的文本

        Returns:
            bool: 复制是否成功
        """
        try:
            import platform
            import subprocess

            system = platform.system().lower()

            if system == 'linux':
                # 尝试使用xclip命令
                try:
                    # 复制到主剪贴板
                    subprocess.run(['xclip', '-selection', 'clipboard'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    # 复制到选择剪贴板
                    subprocess.run(['xclip', '-selection', 'primary'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    print(f"Linux xclip复制成功: {text[:50]}...")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    # 尝试使用xsel命令
                    try:
                        subprocess.run(['xsel', '--clipboard', '--input'],
                                     input=text.encode('utf-8'),
                                     check=True,
                                     timeout=2)
                        subprocess.run(['xsel', '--primary', '--input'],
                                     input=text.encode('utf-8'),
                                     check=True,
                                     timeout=2)
                        print(f"Linux xsel复制成功: {text[:50]}...")
                        return True
                    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                        print("Linux备用剪贴板工具不可用")
                        return False

            elif system == 'windows':
                # Windows使用clip命令
                try:
                    subprocess.run(['clip'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    print(f"Windows clip复制成功: {text[:50]}...")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    print("Windows clip命令不可用")
                    return False

            elif system == 'darwin':  # macOS
                # macOS使用pbcopy命令
                try:
                    subprocess.run(['pbcopy'],
                                 input=text.encode('utf-8'),
                                 check=True,
                                 timeout=2)
                    print(f"macOS pbcopy复制成功: {text[:50]}...")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    print("macOS pbcopy命令不可用")
                    return False

            return False

        except Exception as e:
            print(f"备用剪贴板复制异常: {str(e)}")
            return False

    def restore_status_text(self):
        """恢复状态栏文本"""
        if hasattr(self, 'current_case_name') and self.current_case_name:
            self.update_progress_display()
        else:
            self.status_label.setText("就绪")

    def connect_signals(self):
        """连接信号槽"""
        # 数据模型信号 - 使用Qt.QueuedConnection避免死锁
        self.data_model.violation_added.connect(self.on_violation_added, Qt.QueuedConnection)
        self.data_model.violation_updated.connect(self.on_violation_updated, Qt.QueuedConnection)
        self.data_model.confirmation_updated.connect(self.on_confirmation_updated, Qt.QueuedConnection)

        # 界面信号
        self.corner_combo.currentTextChanged.connect(self.on_corner_changed)
        self.case_name_edit.textChanged.connect(self.on_case_name_changed)

        # 标准表格双击事件
        self.violation_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

        # 高性能表格信号
        self.high_performance_table.cell_double_clicked.connect(self.on_high_performance_cell_double_clicked)
        self.high_performance_table.action_button_clicked.connect(self.on_high_performance_action_clicked)

    def apply_runsim_theme(self):
        """应用RunSim GUI主题样式"""
        runsim_style = """
            /* 主窗口样式 - 与 Runsim GUI 保持一致 */
            QMainWindow, QDialog {
                background-color: #f5f5f5;
                color: #444;
            }

            /* 分组框样式 */
            QGroupBox {
                font-family: "Microsoft YaHei";
                font-weight: bold;
                border: 2px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding: 10px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
                color: #444;
            }

            /* 按钮样式 */
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                min-width: 80px;
            }

            QPushButton:hover {
                background-color: #3d8ced;
            }

            QPushButton:pressed {
                background-color: #3274bf;
            }

            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }

            /* 输入框样式 */
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background: white;
                selection-background-color: #4a9eff;
                font-family: "Microsoft YaHei";
            }

            QLineEdit:focus {
                border: 2px solid #4a9eff;
            }

            /* 下拉框样式 */
            QComboBox {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background: white;
                font-family: "Microsoft YaHei";
                min-width: 100px;
            }

            QComboBox:focus {
                border: 2px solid #4a9eff;
            }

            QComboBox::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }

            /* 表格样式 */
            QTableWidget {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                gridline-color: #e0e0e0;
                font-family: "Microsoft YaHei";
                selection-background-color: #4a9eff;
                selection-color: white;
            }

            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #e0e0e0;
            }

            QTableWidget::item:hover {
                background-color: #f0f8ff;
            }

            QTableWidget::item:selected {
                background-color: #4a9eff;
                color: white;
            }

            QHeaderView::section {
                background-color: #f8f8f8;
                border: 1px solid #d0d0d0;
                padding: 5px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
            }

            /* 进度条样式 */
            QProgressBar {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                text-align: center;
                font-family: "Microsoft YaHei";
            }

            QProgressBar::chunk {
                background-color: #4a9eff;
                border-radius: 3px;
            }

            /* 状态栏样式 */
            QStatusBar {
                background-color: #f0f0f0;
                border-top: 1px solid #d0d0d0;
                color: #666;
                font-family: "Microsoft YaHei";
            }

            /* 工具栏样式 */
            QToolBar {
                background-color: #f8f8f8;
                border-bottom: 1px solid #d0d0d0;
                spacing: 3px;
                padding: 3px;
            }

            QToolBar::separator {
                background-color: #d0d0d0;
                width: 1px;
                margin: 0 5px;
            }
        """
        self.setStyleSheet(runsim_style)

    def select_log_file(self):
        """选择日志文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择时序违例日志文件",
            "",
            "日志文件 (vio_summary.log);;所有文件 (*)"
        )

        if file_path:
            self.load_log_file(file_path)

    def load_log_file(self, file_path: str):
        """加载日志文件"""
        try:
            # 更新文件路径显示
            self.file_path_edit.setText(file_path)
            self.current_file_path = file_path

            # 解析用例信息
            dir_path = os.path.dirname(os.path.dirname(file_path))  # 去掉/log/vio_summary.log
            case_info = CaseInfoParser.parse_directory_name(dir_path)

            # 更新用例信息
            self.case_name_edit.setText(case_info['case_name'])
            self.current_case_name = case_info['case_name']

            if case_info['corner']:
                # 找到对应的corner并选中
                index = self.corner_combo.findText(case_info['corner'])
                if index >= 0:
                    self.corner_combo.setCurrentIndex(index)
                    self.current_corner = case_info['corner']
                    print(f"设置corner为: {self.current_corner}")
                else:
                    self.corner_combo.setCurrentIndex(0)
                    self.current_corner = ""
                    print("未找到匹配的corner，设置为空")
            else:
                self.corner_combo.setCurrentIndex(0)
                self.current_corner = ""
                print("没有corner信息，设置为空")

            # 异步解析文件
            self.parse_file_async(file_path)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载文件失败: {str(e)}")

    def parse_file_async(self, file_path: str):
        """智能异步解析文件"""
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        # 禁用相关按钮
        self.select_file_btn.setEnabled(False)
        self.auto_confirm_btn.setEnabled(False)

        # 智能选择解析器
        file_size = os.path.getsize(file_path)
        file_size_mb = file_size / (1024 * 1024)

        # 根据文件大小选择解析策略 - 降低阈值，更早使用高性能模式
        if file_size_mb > 5:  # 大于5MB就使用高性能解析器，避免GUI卡死
            self.status_label.setText(f"检测到大文件 ({file_size_mb:.1f}MB)，使用高性能解析器...")
            self.async_parser = HighPerformanceAsyncParser(file_path)
        else:
            self.status_label.setText(f"使用标准解析器解析文件 ({file_size_mb:.1f}MB)...")
            self.async_parser = AsyncVioLogParser(file_path)

        # 连接信号
        self.async_parser.progress_updated.connect(self.on_parsing_progress)
        self.async_parser.parsing_completed.connect(self.on_parsing_completed)
        self.async_parser.parsing_failed.connect(self.on_parsing_failed)

        # 开始解析
        self.async_parser.start()

    def on_parsing_progress(self, progress: int, message: str):
        """解析进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)

    def on_parsing_completed(self, violations: List[Dict]):
        """解析完成"""
        try:
            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 启用按钮
            self.select_file_btn.setEnabled(True)
            self.auto_confirm_btn.setEnabled(True)

            # 保存违例数据
            self.current_violations = violations

            # 显示性能统计信息
            self._show_performance_stats(violations)

            # 清除旧数据
            if self.current_case_name:
                stored_corner = self.get_stored_corner()
                self.data_model.clear_case_data(self.current_case_name, stored_corner)

            # 添加新数据到数据库
            if violations and self.current_case_name:
                stored_corner = self.get_stored_corner()
                success_count = self.data_model.add_violations(
                    violations, self.current_case_name, stored_corner, self.current_file_path
                )
                print(f"成功添加 {success_count} 条违例记录到数据库")

                # 自动应用历史确认记录
                applied_count = self.data_model.apply_historical_confirmations(
                    self.current_case_name, stored_corner
                )
                if applied_count > 0:
                    #print(f"自动应用历史确认记录: {applied_count} 条")
                    self.status_label.setText(f"解析完成，共 {len(violations)} 条违例记录，自动应用历史确认 {applied_count} 条")
                else:
                    self.status_label.setText(f"解析完成，共 {len(violations)} 条违例记录")

            # 更新表格显示
            self.update_violation_table()

            # 更新进度显示
            self.update_progress_display()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理解析结果失败: {str(e)}")

    def _show_performance_stats(self, violations: List[Dict]):
        """显示性能统计信息"""
        try:
            # 获取解析器统计信息
            if hasattr(self.async_parser, 'parser') and hasattr(self.async_parser.parser, 'parse_stats'):
                stats = self.async_parser.parser.parse_stats
                total_lines = stats.get('total_lines', 0)
                parse_time = stats.get('parse_time', 0)

                if parse_time > 0:
                    throughput = len(violations) / parse_time
                    lines_per_sec = total_lines / parse_time

                    print(f"=== 解析性能统计 ===")
                    print(f"总行数: {total_lines:,}")
                    print(f"违例记录: {len(violations):,}")
                    print(f"解析时间: {parse_time:.2f}秒")
                    print(f"处理速度: {lines_per_sec:.0f} 行/秒")
                    print(f"违例吞吐量: {throughput:.0f} 记录/秒")

                    # 性能建议
                    if parse_time > 10:
                        print(f"性能建议: 文件较大，建议考虑分批处理或使用SSD存储")
                    elif throughput < 1000:
                        print(f"性能建议: 吞吐量较低，可能受到磁盘I/O限制")

        except Exception as e:
            print(f"显示性能统计失败: {str(e)}")

    def on_parsing_failed(self, error_message: str):
        """解析失败"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.select_file_btn.setEnabled(True)

        # 显示错误
        self.status_label.setText("解析失败")
        QMessageBox.critical(self, "解析失败", error_message)

    def update_violation_table(self):
        """更新违例表格显示"""
        if not self.current_case_name:
            self._clear_all_tables()
            return

        # 从数据库获取违例记录
        # 数据存储时使用的corner（从文件路径解析得到的实际corner）
        stored_corner = self.get_stored_corner()
        print(f"查询违例记录 - 用例: {self.current_case_name}, 存储corner: {stored_corner}")
        violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)
        print(f"找到 {len(violations)} 条违例记录")

        # 根据数据量选择表格类型
        if len(violations) > self.performance_threshold:
            self._use_high_performance_table(violations)
        else:
            self._use_standard_table(violations)

    def _clear_all_tables(self):
        """清空所有表格"""
        self.violation_table.setRowCount(0)
        self.high_performance_table.update_data([])
        self.performance_info_label.setVisible(False)

    def _use_high_performance_table(self, violations):
        """使用高性能表格显示大数据集"""
        print(f"使用高性能表格显示 {len(violations)} 条记录")

        # 隐藏标准表格，显示高性能表格
        self.violation_table.setVisible(False)
        self.high_performance_table.setVisible(True)

        # 显示性能提示
        self.performance_info_label.setText(
            f"检测到大数据集({len(violations)}条记录)，已启用高性能模式以提升加载速度"
        )
        self.performance_info_label.setVisible(True)

        # 更新高性能表格数据
        self.high_performance_table.update_data(violations)
        self.use_high_performance_table = True

    def _use_standard_table(self, violations):
        """使用标准表格显示小数据集"""
        print(f"使用标准表格显示 {len(violations)} 条记录")

        # 显示标准表格，隐藏高性能表格
        self.violation_table.setVisible(True)
        self.high_performance_table.setVisible(False)
        self.performance_info_label.setVisible(False)

        # 设置表格行数
        self.violation_table.setRowCount(len(violations))
        self.use_high_performance_table = False

        # 填充标准表格数据
        self._fill_standard_table(violations)

    def _fill_standard_table(self, violations):
        """填充标准表格数据"""
        for row, violation in enumerate(violations):
            status = violation.get('status', 'pending')
            is_confirmed = status in ['confirmed', 'ignored']

            # 定义已确认条目的灰色样式
            gray_text_color = QColor(128, 128, 128)  # 灰色文字
            normal_text_color = QColor(0, 0, 0)      # 正常黑色文字

            # NUM
            num_item = QTableWidgetItem(str(violation.get('num', '')))
            num_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                num_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 0, num_item)

            # 层级路径
            hier_text = violation.get('hier', '')
            hier_item = QTableWidgetItem(hier_text)
            hier_item.setToolTip(hier_text)  # 添加悬停提示
            if is_confirmed:
                hier_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 1, hier_item)

            # 时间(ns)
            time_ns = violation.get('time_fs', 0) / 1000000
            time_item = QTableWidgetItem(f"{time_ns:.3f}")
            time_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                time_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 2, time_item)

            # 检查信息
            check_info_text = violation.get('check_info', '')
            check_item = QTableWidgetItem(check_info_text)
            check_item.setToolTip(check_info_text)  # 添加悬停提示
            if is_confirmed:
                check_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 3, check_item)

            # 状态
            status_item = QTableWidgetItem(self.get_status_display(status))
            status_item.setTextAlignment(Qt.AlignCenter)

            # 设置状态颜色（保持原有的背景色，但文字使用对应颜色）
            if status == 'confirmed':
                status_item.setBackground(QColor(144, 238, 144))  # 浅绿色背景
                status_item.setForeground(QColor(0, 100, 0))     # 深绿色文字
            elif status == 'ignored':
                status_item.setBackground(QColor(255, 182, 193))  # 浅红色背景
                status_item.setForeground(QColor(139, 0, 0))     # 深红色文字
            else:
                status_item.setBackground(QColor(255, 255, 224))  # 浅黄色背景
                status_item.setForeground(QColor(184, 134, 11))  # 深黄色文字

            self.violation_table.setItem(row, 4, status_item)

            # 确认人
            confirmer_item = QTableWidgetItem(violation.get('confirmer', ''))
            confirmer_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                confirmer_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 5, confirmer_item)

            # 确认结果
            result = violation.get('result', '')
            result_item = QTableWidgetItem(self.get_result_display(result))
            result_item.setTextAlignment(Qt.AlignCenter)
            if is_confirmed:
                result_item.setForeground(gray_text_color)
            self.violation_table.setItem(row, 6, result_item)

            # 操作按钮
            if status == 'pending':
                confirm_btn = QPushButton("确认")
                confirm_btn.setMaximumWidth(80)
                confirm_btn.clicked.connect(lambda checked, v_id=violation.get('id'): self.confirm_single_violation(v_id))
                self.violation_table.setCellWidget(row, 7, confirm_btn)
            else:
                edit_btn = QPushButton("编辑")
                edit_btn.setMaximumWidth(80)
                # 为已确认条目的按钮也应用灰色样式
                edit_btn.setStyleSheet("""
                    QPushButton {
                        color: #808080;
                        background-color: #f0f0f0;
                        border: 1px solid #c0c0c0;
                    }
                    QPushButton:hover {
                        background-color: #e0e0e0;
                    }
                """)
                edit_btn.clicked.connect(lambda checked, v_id=violation.get('id'): self.edit_confirmation(v_id))
                self.violation_table.setCellWidget(row, 7, edit_btn)

    def on_high_performance_cell_double_clicked(self, row, column):
        """处理高性能表格单元格双击事件"""
        # 只处理层级路径列（第1列，索引为1）
        if column == 1:
            # 获取实际行索引（考虑滚动偏移）
            actual_row = self.high_performance_table.first_visible_row + row
            violation = self.high_performance_table.model.get_violation_at_row(actual_row)

            if violation:
                hier_path = violation.get('hier', '')
                if hier_path:
                    # 跨平台剪贴板复制
                    success = self._copy_to_clipboard(hier_path)

                    if success:
                        # 显示成功提示消息
                        self.status_label.setText(f"已复制层级路径: {hier_path[:50]}{'...' if len(hier_path) > 50 else ''}")
                    else:
                        # 显示失败提示消息
                        self.status_label.setText(f"复制失败，请手动选择文本: {hier_path[:30]}...")

                    # 使用QTimer在3秒后恢复原状态文本
                    QTimer.singleShot(3000, self.restore_status_text)

    def on_high_performance_action_clicked(self, row, action_type):
        """处理高性能表格操作按钮点击事件"""
        # 获取实际行索引（考虑滚动偏移）
        actual_row = self.high_performance_table.first_visible_row + row
        violation = self.high_performance_table.model.get_violation_at_row(actual_row)

        if violation:
            violation_id = violation.get('id')
            if action_type == 'pending':
                self.confirm_single_violation(violation_id)
            else:
                self.edit_confirmation(violation_id)

    def get_stored_corner(self) -> str:
        """获取数据存储时使用的corner

        Returns:
            str: 存储时使用的corner名称
        """
        # 如果用户已经选择了具体的corner，优先使用用户选择的corner
        if self.current_corner:
            return self.current_corner

        # 如果当前文件路径中包含corner信息，使用解析出的corner
        if self.current_file_path:
            dir_path = os.path.dirname(os.path.dirname(self.current_file_path))
            case_info = CaseInfoParser.parse_directory_name(dir_path)
            if case_info['corner']:
                return case_info['corner']

        # 否则使用default
        return "default"

    def get_display_corner(self) -> str:
        """获取显示用的corner名称（用于文件名等）

        Returns:
            str: 显示用的corner名称
        """
        if self.current_corner:
            return self.current_corner

        # 如果没有选择corner，使用存储的corner
        stored_corner = self.get_stored_corner()
        return stored_corner if stored_corner != "default" else "default"

    def get_status_display(self, status: str) -> str:
        """获取状态显示文本"""
        status_map = {
            'pending': '待确认',
            'confirmed': '已确认',
            'ignored': '已忽略'
        }
        return status_map.get(status, status)

    def get_result_display(self, result: str) -> str:
        """获取结果显示文本"""
        result_map = {
            'pass': '通过',
            'issue': '有问题',
            '': ''
        }
        return result_map.get(result, result)

    def auto_confirm_violations(self):
        """自动确认违例"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "请先选择日志文件")
            return

        # 获取复位时间
        try:
            reset_time_text = self.reset_time_edit.text().strip()
            if not reset_time_text:
                QMessageBox.warning(self, "警告", "请输入复位时间")
                return

            reset_time_ns = float(reset_time_text)
            if reset_time_ns <= 0:
                QMessageBox.warning(self, "警告", "复位时间必须大于0")
                return
        except ValueError:
            QMessageBox.warning(self, "警告", "复位时间格式错误，请输入数字")
            return

        # 执行自动确认
        corner = self.current_corner if self.current_corner else "default"
        confirmed_count = self.data_model.auto_confirm_by_reset_time(
            self.current_case_name, corner, reset_time_ns
        )

        if confirmed_count > 0:
            QMessageBox.information(self, "成功", f"已自动确认 {confirmed_count} 条违例记录")
            self.update_violation_table()
            self.update_progress_display()
        else:
            QMessageBox.information(self, "提示", "没有找到需要自动确认的违例记录")

    def confirm_single_violation(self, violation_id: int):
        """确认单个违例"""
        if not violation_id:
            return

        try:
            # 获取违例信息
            corner = self.current_corner if self.current_corner else "default"
            violations = self.data_model.get_violations_by_case(self.current_case_name, corner)
            violation = next((v for v in violations if v.get('id') == violation_id), None)

            if not violation:
                QMessageBox.warning(self, "错误", "找不到违例记录")
                return

            # 检查历史建议
            suggestions = self.data_model.get_pattern_suggestions(
                violation.get('hier', ''), violation.get('check_info', '')
            )

            # 显示确认对话框
            dialog = ConfirmationDialog(self, violation, suggestions)
            dialog.setModal(True)  # 确保模态

            # 使用exec_()而不是show()
            result_code = dialog.exec_()

            if result_code == QDialog.Accepted:
                result = dialog.get_result()

                # 更新确认记录
                update_result = self.data_model.update_confirmation(
                    violation_id,
                    status='confirmed',
                    confirmer=result['confirmer'],
                    result=result['result'],
                    reason=result['reason'],
                    is_auto=False
                )

                # 处理返回结果
                if isinstance(update_result, tuple):
                    success, confirmation_data = update_result
                else:
                    success = update_result
                    confirmation_data = None

                if success:
                    # 保存到历史模式
                    self.data_model.save_pattern(
                        violation.get('hier', ''),
                        violation.get('check_info', ''),
                        result['confirmer'],
                        result['result'],
                        result['reason']
                    )

                    # 使用QTimer延迟更新UI，避免死锁
                    QTimer.singleShot(0, self.safe_update_ui)

                    # 显示成功消息
                    self.status_label.setText("确认记录已更新")
                else:
                    QMessageBox.critical(self, "错误", "更新确认记录失败")

            # 确保对话框被正确销毁
            dialog.deleteLater()

        except Exception as e:
            print(f"确认违例时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"确认过程中出现错误: {str(e)}")

    def update_progress_display(self):
        """更新进度显示"""
        if not self.current_case_name:
            self.progress_label.setText("进度: 已确认 0/0 (0%)")
            self.stats_label.setText("总计: 0条违例 | 已确认: 0条 | 待确认: 0条")
            return

        # 获取统计信息
        stored_corner = self.get_stored_corner()
        violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)

        total_count = len(violations)
        confirmed_count = sum(1 for v in violations if v.get('status') in ['confirmed', 'ignored'])
        pending_count = total_count - confirmed_count

        # 计算百分比
        percentage = (confirmed_count / total_count * 100) if total_count > 0 else 0

        # 更新显示
        self.progress_label.setText(f"进度: 已确认 {confirmed_count}/{total_count} ({percentage:.1f}%)")
        self.stats_label.setText(f"总计: {total_count}条违例 | 已确认: {confirmed_count}条 | 待确认: {pending_count}条")

    def update_time_display(self):
        """更新时间显示"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"最后更新: {current_time}")

    def safe_update_ui(self):
        """安全的UI更新方法，避免死锁"""
        try:
            self.update_violation_table()
            self.update_progress_display()
        except Exception as e:
            print(f"UI更新失败: {str(e)}")
            import traceback
            traceback.print_exc()

    # 信号槽方法
    def on_violation_added(self, violation_data: Dict):
        """违例添加信号处理"""
        pass  # 在批量添加时不需要单独处理

    def on_violation_updated(self, violation_data: Dict):
        """违例更新信号处理"""
        # 使用QTimer延迟更新，避免死锁
        QTimer.singleShot(0, self.safe_update_ui)

    def on_confirmation_updated(self, confirmation_data: Dict):
        """确认更新信号处理"""
        # 使用QTimer延迟更新，避免死锁
        QTimer.singleShot(0, self.safe_update_ui)

    def on_corner_changed(self, corner: str):
        """Corner选择改变 - 更新corner标记，如果是从default切换则更新数据库"""
        print(f"Corner选择改变: '{corner}'")

        # 更新当前corner标记
        old_corner = self.current_corner
        if corner != "请选择...":
            self.current_corner = corner
        else:
            self.current_corner = ""

        print(f"Corner标记从 '{old_corner}' 变更为 '{self.current_corner}'")

        # 如果有用例数据，检查是否需要更新数据库中的corner
        if self.current_case_name and self.current_corner:
            stored_corner = self.get_stored_corner()

            # 如果当前存储的是default，且用户选择了具体corner，则更新数据库
            if stored_corner == "default" and self.current_corner != "default":
                print(f"检测到从default切换到具体corner，更新数据库...")
                success = self.data_model.update_case_corner(
                    self.current_case_name, "default", self.current_corner
                )

                if success:
                    print(f"成功更新数据库corner从 'default' 到 '{self.current_corner}'")
                    # 更新文件路径以反映新的corner
                    if self.current_file_path:
                        # 重新构造文件路径以包含corner信息
                        dir_path = os.path.dirname(os.path.dirname(self.current_file_path))
                        new_dir_path = f"{dir_path}_{self.current_corner}"
                        self.current_file_path = os.path.join(new_dir_path, "log", "vio_summary.log")
                        print(f"更新文件路径: {self.current_file_path}")
                else:
                    print("更新数据库corner失败")

        # 更新进度显示
        if self.current_case_name:
            self.update_progress_display()

        print("Corner切换完成")

    def on_case_name_changed(self, case_name: str):
        """用例名称改变"""
        self.current_case_name = case_name.strip()
        if self.current_case_name:
            self.update_violation_table()
            self.update_progress_display()

    def batch_confirm_violations(self):
        """批量确认违例"""
        if self.use_high_performance_table:
            QMessageBox.information(
                self, "提示",
                "高性能模式下暂不支持批量确认功能，请使用单个确认或全部确认功能。"
            )
            return

        # 获取选中的行
        selected_rows = set()
        for item in self.violation_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要确认的违例记录")
            return

        # 显示批量确认对话框
        dialog = BatchConfirmationDialog(self)
        dialog.setModal(True)
        result_code = dialog.exec_()

        if result_code == QDialog.Accepted:
            result = dialog.get_result()

            # 获取选中违例的ID
            corner = self.current_corner if self.current_corner else "default"
            violations = self.data_model.get_violations_by_case(self.current_case_name, corner)

            success_count = 0
            for row in selected_rows:
                if row < len(violations):
                    violation = violations[row]
                    violation_id = violation.get('id')

                    if violation.get('status') == 'pending':
                        update_result = self.data_model.update_confirmation(
                            violation_id,
                            status='confirmed',
                            confirmer=result['confirmer'],
                            result=result['result'],
                            reason=result['reason'],
                            is_auto=False
                        )

                        # 处理返回结果
                        if isinstance(update_result, tuple):
                            success, _ = update_result
                        else:
                            success = update_result

                        if success:
                            success_count += 1
                            # 保存到历史模式
                            self.data_model.save_pattern(
                                violation.get('hier', ''),
                                violation.get('check_info', ''),
                                result['confirmer'],
                                result['result'],
                                result['reason']
                            )

            if success_count > 0:
                QMessageBox.information(self, "成功", f"已确认 {success_count} 条违例记录")
                # 使用QTimer延迟更新UI，避免死锁
                QTimer.singleShot(0, self.safe_update_ui)
            else:
                QMessageBox.warning(self, "警告", "没有可确认的记录")

        # 确保对话框被正确销毁
        dialog.deleteLater()

    def confirm_all_violations(self):
        """确认所有违例"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "请先选择日志文件")
            return

        # 获取待确认的违例数量
        corner = self.current_corner if self.current_corner else "default"
        violations = self.data_model.get_violations_by_case(self.current_case_name, corner)
        pending_violations = [v for v in violations if v.get('status') == 'pending']

        if not pending_violations:
            QMessageBox.information(self, "提示", "没有待确认的违例记录")
            return

        # 确认操作
        reply = QMessageBox.question(
            self, "确认",
            f"确定要确认所有 {len(pending_violations)} 条待确认的违例记录吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 显示批量确认对话框
            dialog = BatchConfirmationDialog(self)
            dialog.setModal(True)
            result_code = dialog.exec_()

            if result_code == QDialog.Accepted:
                result = dialog.get_result()

                success_count = 0
                for violation in pending_violations:
                    violation_id = violation.get('id')
                    update_result = self.data_model.update_confirmation(
                        violation_id,
                        status='confirmed',
                        confirmer=result['confirmer'],
                        result=result['result'],
                        reason=result['reason'],
                        is_auto=False
                    )

                    # 处理返回结果
                    if isinstance(update_result, tuple):
                        success, _ = update_result
                    else:
                        success = update_result

                    if success:
                        success_count += 1
                        # 保存到历史模式
                        self.data_model.save_pattern(
                            violation.get('hier', ''),
                            violation.get('check_info', ''),
                            result['confirmer'],
                            result['result'],
                            result['reason']
                        )

                if success_count > 0:
                    QMessageBox.information(self, "成功", f"已确认 {success_count} 条违例记录")
                    # 使用QTimer延迟更新UI，避免死锁
                    QTimer.singleShot(0, self.safe_update_ui)

            # 确保对话框被正确销毁
            dialog.deleteLater()

    def edit_confirmation(self, violation_id: int):
        """编辑确认记录"""
        if not violation_id:
            return

        try:
            # 获取违例信息
            corner = self.current_corner if self.current_corner else "default"
            violations = self.data_model.get_violations_by_case(self.current_case_name, corner)
            violation = next((v for v in violations if v.get('id') == violation_id), None)

            if not violation:
                QMessageBox.warning(self, "错误", "找不到违例记录")
                return

            # 显示编辑对话框
            dialog = ConfirmationDialog(self, violation, None, edit_mode=True)
            dialog.setModal(True)  # 确保模态

            result_code = dialog.exec_()

            if result_code == QDialog.Accepted:
                result = dialog.get_result()

                # 更新确认记录
                update_result = self.data_model.update_confirmation(
                    violation_id,
                    status='confirmed',
                    confirmer=result['confirmer'],
                    result=result['result'],
                    reason=result['reason'],
                    is_auto=False
                )

                # 处理返回结果
                if isinstance(update_result, tuple):
                    success, _ = update_result
                else:
                    success = update_result

                if success:
                    # 更新历史模式
                    self.data_model.save_pattern(
                        violation.get('hier', ''),
                        violation.get('check_info', ''),
                        result['confirmer'],
                        result['result'],
                        result['reason']
                    )

                    # 使用QTimer延迟更新UI，避免死锁
                    QTimer.singleShot(0, self.safe_update_ui)

                    # 显示成功消息
                    self.status_label.setText("确认记录已更新")
                else:
                    QMessageBox.critical(self, "错误", "更新确认记录失败")

            # 确保对话框被正确销毁
            dialog.deleteLater()

        except Exception as e:
            print(f"编辑确认时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"编辑过程中出现错误: {str(e)}")

    def refresh_data(self):
        """刷新数据"""
        if self.current_file_path:
            self.load_log_file(self.current_file_path)
        else:
            self.update_violation_table()
            self.update_progress_display()

    def export_to_excel(self):
        """导出为Excel文件"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "没有数据可导出")
            return

        # 检查表格中是否有数据
        table_row_count = self.violation_table.rowCount()
        print(f"导出Excel - 表格中有 {table_row_count} 行数据")

        if table_row_count == 0:
            QMessageBox.warning(self, "警告", "表格中没有数据可导出，请先加载违例数据")
            return

        # 构建默认保存路径
        corner_name = self.get_display_corner()
        default_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK", corner_name)

        # 确保目录存在
        os.makedirs(default_dir, exist_ok=True)

        default_name = f"{self.current_case_name}_{corner_name}_violations_checklist.xlsx"
        default_path = os.path.join(default_dir, default_name)

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出Excel文件", default_path, "Excel文件 (*.xlsx)"
        )

        if file_path:
            try:
                self._export_data_to_excel(file_path)
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def export_to_csv(self):
        """导出为CSV文件"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "没有数据可导出")
            return

        # 检查表格中是否有数据
        table_row_count = self.violation_table.rowCount()
        print(f"导出CSV - 表格中有 {table_row_count} 行数据")

        if table_row_count == 0:
            QMessageBox.warning(self, "警告", "表格中没有数据可导出，请先加载违例数据")
            return

        # 构建默认保存路径
        corner_name = self.get_display_corner()
        default_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK", corner_name)

        # 确保目录存在
        os.makedirs(default_dir, exist_ok=True)

        default_name = f"{self.current_case_name}_{corner_name}_violations_checklist.csv"
        default_path = os.path.join(default_dir, default_name)

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出CSV文件", default_path, "CSV文件 (*.csv)"
        )

        if file_path:
            try:
                self._export_data_to_csv(file_path)
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def apply_historical_confirmations(self):
        """手动应用历史确认记录"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "请先选择日志文件")
            return

        try:
            corner = self.current_corner if self.current_corner else "default"
            applied_count = self.data_model.apply_historical_confirmations(
                self.current_case_name, corner
            )

            if applied_count > 0:
                QMessageBox.information(
                    self, "成功",
                    f"已自动应用 {applied_count} 条历史确认记录"
                )
                # 使用QTimer延迟更新UI，避免死锁
                QTimer.singleShot(0, self.safe_update_ui)
            else:
                QMessageBox.information(
                    self, "提示",
                    "没有找到可应用的历史确认记录"
                )
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用历史确认失败: {str(e)}")

    def clear_history(self):
        """清除历史数据"""
        if not self.current_case_name:
            QMessageBox.warning(self, "警告", "没有数据可清除")
            return

        reply = QMessageBox.question(
            self, "确认",
            f"确定要清除用例 '{self.current_case_name}' 的所有历史数据吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                corner = self.current_corner if self.current_corner else "default"
                self.data_model.clear_case_data(self.current_case_name, corner)
                self.update_violation_table()
                self.update_progress_display()
                QMessageBox.information(self, "成功", "历史数据已清除")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"清除数据失败: {str(e)}")

    def show_history_management(self):
        """显示历史管理对话框"""
        try:
            dialog = HistoryManagementDialog(self, self.data_model)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开历史管理失败: {str(e)}")

    def _export_data_to_excel(self, file_path: str):
        """导出数据到Excel文件"""
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill
        except ImportError:
            raise ImportError("需要安装openpyxl库: pip install openpyxl")

        # 获取数据 - 优先从数据库获取，如果没有则提示用户
        stored_corner = self.get_stored_corner()
        violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)

        # 调试信息
        print(f"导出Excel - 用例: {self.current_case_name}, Corner: {stored_corner}")
        print(f"导出Excel - 从数据库获取到 {len(violations)} 条违例记录")

        # 如果数据库中没有数据，尝试从当前内存中的数据获取
        if not violations and hasattr(self, 'current_violations') and self.current_violations:
            print("导出Excel - 数据库中无数据，使用内存中的数据")
            violations = self.current_violations
            print(f"导出Excel - 从内存获取到 {len(violations)} 条违例记录")
            # 调试：显示第一条记录的字段
            if violations:
                first_record = violations[0]
                print(f"导出Excel - 数据格式示例: {list(first_record.keys())[:5]}...")

        if not violations:
            raise ValueError(f"没有找到用例 '{self.current_case_name}' (corner: {stored_corner}) 的违例数据。请先加载违例日志文件。")

        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "时序违例确认清单"

        # 设置表头
        headers = ["序号", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "确认理由", "确认时间"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # 填充数据
        for row, violation in enumerate(violations, 2):
            # 兼容两种数据格式：数据库格式(小写)和解析器格式(大写)
            num = violation.get('num', violation.get('NUM', ''))
            hier = violation.get('hier', violation.get('Hier', ''))
            time_fs = violation.get('time_fs', violation.get('time_fs', 0))
            check_info = violation.get('check_info', violation.get('Check', ''))
            status = violation.get('status', '')
            confirmer = violation.get('confirmer', '')
            result = violation.get('result', '')
            reason = violation.get('reason', '')
            confirmed_at = violation.get('confirmed_at', '')

            print(f"导出Excel - 处理第 {row-1} 条记录: {hier[:50]}...")

            ws.cell(row=row, column=1, value=num)
            ws.cell(row=row, column=2, value=hier)
            ws.cell(row=row, column=3, value=time_fs / 1000000 if time_fs else 0)
            ws.cell(row=row, column=4, value=check_info)
            ws.cell(row=row, column=5, value=self.get_status_display(status))
            ws.cell(row=row, column=6, value=confirmer)
            ws.cell(row=row, column=7, value=self.get_result_display(result))
            ws.cell(row=row, column=8, value=reason)
            ws.cell(row=row, column=9, value=confirmed_at)

        print(f"导出Excel - 完成数据填充，共处理 {len(violations)} 条记录")

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 保存文件
        wb.save(file_path)

    def _export_data_to_csv(self, file_path: str):
        """导出数据到CSV文件"""
        import csv

        # 获取数据 - 优先从数据库获取，如果没有则提示用户
        stored_corner = self.get_stored_corner()
        violations = self.data_model.get_violations_by_case(self.current_case_name, stored_corner)

        # 调试信息
        print(f"导出CSV - 用例: {self.current_case_name}, Corner: {stored_corner}")
        print(f"导出CSV - 从数据库获取到 {len(violations)} 条违例记录")

        # 如果数据库中没有数据，尝试从当前内存中的数据获取
        if not violations and hasattr(self, 'current_violations') and self.current_violations:
            print("导出CSV - 数据库中无数据，使用内存中的数据")
            violations = self.current_violations
            print(f"导出CSV - 从内存获取到 {len(violations)} 条违例记录")
            # 调试：显示第一条记录的字段
            if violations:
                first_record = violations[0]
                print(f"导出CSV - 数据格式示例: {list(first_record.keys())[:5]}...")

        if not violations:
            raise ValueError(f"没有找到用例 '{self.current_case_name}' (corner: {stored_corner}) 的违例数据。请先加载违例日志文件。")

        # 写入CSV文件
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            headers = ["序号", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "确认理由", "确认时间"]
            writer.writerow(headers)

            # 写入数据
            for i, violation in enumerate(violations):
                # 兼容两种数据格式：数据库格式(小写)和解析器格式(大写)
                num = violation.get('num', violation.get('NUM', ''))
                hier = violation.get('hier', violation.get('Hier', ''))
                time_fs = violation.get('time_fs', violation.get('time_fs', 0))
                check_info = violation.get('check_info', violation.get('Check', ''))
                status = violation.get('status', '')
                confirmer = violation.get('confirmer', '')
                result = violation.get('result', '')
                reason = violation.get('reason', '')
                confirmed_at = violation.get('confirmed_at', '')

                print(f"导出CSV - 处理第 {i+1} 条记录: {hier[:50]}...")

                row = [
                    num,
                    hier,
                    time_fs / 1000000 if time_fs else 0,
                    check_info,
                    self.get_status_display(status),
                    confirmer,
                    self.get_result_display(result),
                    reason,
                    confirmed_at
                ]
                writer.writerow(row)

            print(f"导出CSV - 完成数据写入，共处理 {len(violations)} 条记录")


class ConfirmationDialog(QDialog):
    """确认对话框"""

    def __init__(self, parent=None, violation=None, suggestions=None, edit_mode=False):
        super().__init__(parent)
        self.violation = violation
        self.suggestions = suggestions
        self.edit_mode = edit_mode

        self.setWindowTitle("编辑确认信息" if edit_mode else "确认时序违例")
        self.setModal(True)
        self.resize(600, 400)

        # 设置窗口标志，确保对话框正常显示
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        self.init_ui()
        if not edit_mode:  # 只在非编辑模式下应用建议
            self.apply_suggestions()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 违例信息显示
        if self.violation:
            info_group = QGroupBox("违例信息")
            info_layout = QFormLayout(info_group)

            info_layout.addRow("序号:", QLabel(str(self.violation.get('num', ''))))
            info_layout.addRow("层级路径:", QLabel(self.violation.get('hier', '')))

            time_ns = self.violation.get('time_fs', 0) / 1000000
            info_layout.addRow("时间:", QLabel(f"{time_ns:.3f} ns"))

            check_label = QLabel(self.violation.get('check_info', ''))
            check_label.setWordWrap(True)
            info_layout.addRow("检查信息:", check_label)

            layout.addWidget(info_group)

        # 确认信息输入
        confirm_group = QGroupBox("确认信息")
        confirm_layout = QFormLayout(confirm_group)

        # 确认人
        self.confirmer_edit = QLineEdit()
        self.confirmer_edit.setPlaceholderText("请输入确认人姓名")
        confirm_layout.addRow("确认人*:", self.confirmer_edit)

        # 确认结果
        result_layout = QHBoxLayout()
        self.result_group = QButtonGroup()

        self.pass_radio = QRadioButton("通过")
        self.issue_radio = QRadioButton("有问题")
        self.pass_radio.setChecked(True)  # 默认选择通过

        self.result_group.addButton(self.pass_radio, 0)
        self.result_group.addButton(self.issue_radio, 1)

        result_layout.addWidget(self.pass_radio)
        result_layout.addWidget(self.issue_radio)
        result_layout.addStretch()

        confirm_layout.addRow("确认结果*:", result_layout)

        # 确认理由
        self.reason_edit = QTextEdit()
        self.reason_edit.setPlaceholderText("请输入确认理由或解决方案")
        self.reason_edit.setMaximumHeight(100)
        confirm_layout.addRow("确认理由*:", self.reason_edit)

        layout.addWidget(confirm_group)

        # 历史建议
        if self.suggestions and not self.edit_mode:
            suggestion_group = QGroupBox("历史建议")
            suggestion_layout = QVBoxLayout(suggestion_group)

            suggestion_text = f"确认人: {self.suggestions.get('confirmer', '')}\n"
            suggestion_text += f"确认结果: {self.suggestions.get('result', '')}\n"
            suggestion_text += f"确认理由: {self.suggestions.get('reason', '')}\n"
            suggestion_text += f"使用次数: {self.suggestions.get('match_count', 0)}"

            suggestion_label = QLabel(suggestion_text)
            suggestion_label.setWordWrap(True)
            suggestion_layout.addWidget(suggestion_label)

            apply_btn = QPushButton("应用建议")
            apply_btn.clicked.connect(self.apply_suggestions)
            suggestion_layout.addWidget(apply_btn)

            layout.addWidget(suggestion_group)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 如果是编辑模式，填充现有数据
        if self.edit_mode and self.violation:
            self.confirmer_edit.setText(self.violation.get('confirmer', ''))

            result = self.violation.get('result', '')
            if result == 'pass':
                self.pass_radio.setChecked(True)
            elif result == 'issue':
                self.issue_radio.setChecked(True)

            self.reason_edit.setPlainText(self.violation.get('reason', ''))

    def apply_suggestions(self):
        """应用历史建议"""
        if not self.suggestions:
            return

        self.confirmer_edit.setText(self.suggestions.get('confirmer', ''))

        result = self.suggestions.get('result', '')
        if result == 'pass':
            self.pass_radio.setChecked(True)
        elif result == 'issue':
            self.issue_radio.setChecked(True)

        self.reason_edit.setPlainText(self.suggestions.get('reason', ''))

    def accept(self):
        """确认按钮点击"""
        # 验证输入
        if not self.confirmer_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入确认人姓名")
            return

        if not self.reason_edit.toPlainText().strip():
            QMessageBox.warning(self, "警告", "请输入确认理由")
            return

        super().accept()

    def get_result(self):
        """获取确认结果"""
        result = 'pass' if self.pass_radio.isChecked() else 'issue'

        return {
            'confirmer': self.confirmer_edit.text().strip(),
            'result': result,
            'reason': self.reason_edit.toPlainText().strip()
        }


class BatchConfirmationDialog(QDialog):
    """批量确认对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("批量确认")
        self.setModal(True)
        self.resize(400, 250)

        # 设置窗口标志，确保对话框正常显示
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 说明
        info_label = QLabel("请填写批量确认信息，将应用到所有选中的违例记录：")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 确认信息输入
        form_layout = QFormLayout()

        # 确认人
        self.confirmer_edit = QLineEdit()
        self.confirmer_edit.setPlaceholderText("请输入确认人姓名")
        form_layout.addRow("确认人*:", self.confirmer_edit)

        # 确认结果
        result_layout = QHBoxLayout()
        self.result_group = QButtonGroup()

        self.pass_radio = QRadioButton("通过")
        self.issue_radio = QRadioButton("有问题")
        self.pass_radio.setChecked(True)  # 默认选择通过

        self.result_group.addButton(self.pass_radio, 0)
        self.result_group.addButton(self.issue_radio, 1)

        result_layout.addWidget(self.pass_radio)
        result_layout.addWidget(self.issue_radio)
        result_layout.addStretch()

        form_layout.addRow("确认结果*:", result_layout)

        # 确认理由
        self.reason_edit = QTextEdit()
        self.reason_edit.setPlaceholderText("请输入确认理由或解决方案")
        self.reason_edit.setMaximumHeight(80)
        form_layout.addRow("确认理由*:", self.reason_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def accept(self):
        """确认按钮点击"""
        # 验证输入
        if not self.confirmer_edit.text().strip():
            QMessageBox.warning(self, "警告", "请输入确认人姓名")
            return

        if not self.reason_edit.toPlainText().strip():
            QMessageBox.warning(self, "警告", "请输入确认理由")
            return

        super().accept()

    def get_result(self):
        """获取确认结果"""
        result = 'pass' if self.pass_radio.isChecked() else 'issue'

        return {
            'confirmer': self.confirmer_edit.text().strip(),
            'result': result,
            'reason': self.reason_edit.toPlainText().strip()
        }


class HistoryManagementDialog(QDialog):
    """历史管理对话框"""

    def __init__(self, parent=None, data_model=None):
        super().__init__(parent)
        self.data_model = data_model

        self.setWindowTitle("历史确认模式管理")
        self.setModal(True)
        self.resize(1000, 600)

        # 设置窗口标志
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        self.init_ui()
        self.load_patterns()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 说明标签
        info_label = QLabel("历史确认模式管理 - 显示所有已保存的确认模式，可以查看和删除")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_patterns)
        toolbar_layout.addWidget(self.refresh_btn)

        self.clear_all_btn = QPushButton("清除全部")
        self.clear_all_btn.clicked.connect(self.clear_all_patterns)
        toolbar_layout.addWidget(self.clear_all_btn)

        toolbar_layout.addStretch()

        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        toolbar_layout.addWidget(self.close_btn)

        layout.addLayout(toolbar_layout)

        # 历史模式表格
        self.patterns_table = QTableWidget()
        self.patterns_table.setColumnCount(7)

        headers = ["层级路径", "检查信息", "确认人", "确认结果", "确认理由", "使用次数", "最后使用时间"]
        self.patterns_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.patterns_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.patterns_table.setAlternatingRowColors(True)
        self.patterns_table.setSortingEnabled(True)

        # 设置列宽
        header = self.patterns_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 层级路径
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 检查信息
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # 确认人
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # 确认结果
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # 确认理由
        header.setSectionResizeMode(5, QHeaderView.Fixed)    # 使用次数
        header.setSectionResizeMode(6, QHeaderView.Fixed)    # 最后使用时间

        # 设置固定列宽
        self.patterns_table.setColumnWidth(2, 100)  # 确认人
        self.patterns_table.setColumnWidth(3, 80)   # 确认结果
        self.patterns_table.setColumnWidth(5, 80)   # 使用次数
        self.patterns_table.setColumnWidth(6, 150)  # 最后使用时间

        layout.addWidget(self.patterns_table)

        # 统计信息
        self.stats_label = QLabel("总计: 0 个历史模式")
        layout.addWidget(self.stats_label)

    def load_patterns(self):
        """加载历史模式"""
        try:
            patterns = self.data_model.get_all_patterns()

            # 设置表格行数
            self.patterns_table.setRowCount(len(patterns))

            # 填充数据
            for row, pattern in enumerate(patterns):
                # 层级路径
                hier_item = QTableWidgetItem(pattern.get('hier_pattern', ''))
                self.patterns_table.setItem(row, 0, hier_item)

                # 检查信息
                check_item = QTableWidgetItem(pattern.get('check_pattern', ''))
                self.patterns_table.setItem(row, 1, check_item)

                # 确认人
                confirmer_item = QTableWidgetItem(pattern.get('default_confirmer', ''))
                confirmer_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 2, confirmer_item)

                # 确认结果
                result = pattern.get('default_result', '')
                result_display = "通过" if result == "pass" else "有问题" if result == "issue" else result
                result_item = QTableWidgetItem(result_display)
                result_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 3, result_item)

                # 确认理由
                reason_item = QTableWidgetItem(pattern.get('default_reason', ''))
                self.patterns_table.setItem(row, 4, reason_item)

                # 使用次数
                count_item = QTableWidgetItem(str(pattern.get('match_count', 0)))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 5, count_item)

                # 最后使用时间
                last_used = pattern.get('last_used', '')
                time_item = QTableWidgetItem(last_used)
                time_item.setTextAlignment(Qt.AlignCenter)
                self.patterns_table.setItem(row, 6, time_item)

            # 更新统计信息
            self.stats_label.setText(f"总计: {len(patterns)} 个历史模式")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载历史模式失败: {str(e)}")

    def clear_all_patterns(self):
        """清除所有历史模式"""
        reply = QMessageBox.question(
            self, "确认",
            "确定要清除所有历史确认模式吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = self.data_model.clear_all_patterns()
                if success:
                    QMessageBox.information(self, "成功", "已清除所有历史模式")
                    self.load_patterns()  # 重新加载
                else:
                    QMessageBox.warning(self, "警告", "清除历史模式失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"清除历史模式失败: {str(e)}")
