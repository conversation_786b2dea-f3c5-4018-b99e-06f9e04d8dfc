#!/usr/bin/env python3
"""
测试timing_violation解析器修复效果
专门测试6万行大文件的解析性能
"""

import sys
import os
import time
import tempfile

# 添加插件路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def generate_large_test_file(file_path: str, violation_count: int = 60000):
    """生成大型测试文件，模拟6万行数据"""
    print(f"生成大型测试文件: {violation_count} 条违例")
    start_time = time.time()
    
    with open(file_path, 'w', encoding='utf-8') as f:
        for i in range(violation_count):
            f.write(f"NUM : {i + 1}\n")
            f.write(f"Hier : tb.cpu_top.core{i % 4}.pipeline.stage{i % 8}.unit_{i}\n")
            f.write(f"Time : {1000000 + (i * 1000)} FS\n")
            f.write(f"Check : Setup time violation on signal data_{i % 16}[{i % 32}]\n")
            f.write("------------------------------------------------------------\n")
    
    gen_time = time.time() - start_time
    file_size = os.path.getsize(file_path)
    file_size_mb = file_size / (1024 * 1024)
    
    print(f"测试文件生成完成:")
    print(f"  文件大小: {file_size_mb:.2f} MB ({file_size:,} 字节)")
    print(f"  生成时间: {gen_time:.2f} 秒")
    print(f"  预期行数: {violation_count * 5:,} 行")


def test_standard_parser(file_path: str):
    """测试标准解析器"""
    print("\n=== 测试标准解析器 ===")
    
    try:
        from parser import VioLogParser
        
        parser = VioLogParser()
        
        # 进度回调
        progress_updates = []
        def progress_callback(progress, message):
            progress_updates.append((progress, message))
            print(f"进度: {progress}% - {message}")
        
        start_time = time.time()
        violations = parser.parse_log_file(file_path, progress_callback)
        parse_time = time.time() - start_time
        
        print(f"✅ 标准解析器测试结果:")
        print(f"   违例数量: {len(violations):,}")
        print(f"   解析时间: {parse_time:.2f} 秒")
        print(f"   处理速度: {len(violations)/parse_time:.0f} 记录/秒")
        print(f"   进度更新: {len(progress_updates)} 次")
        
        return True, parse_time, len(violations)
        
    except Exception as e:
        print(f"❌ 标准解析器测试失败: {e}")
        return False, 0, 0


def test_high_performance_parser(file_path: str):
    """测试高性能解析器"""
    print("\n=== 测试高性能解析器 ===")
    
    try:
        from parser import HighPerformanceVioLogParser
        
        parser = HighPerformanceVioLogParser()
        
        # 进度回调
        progress_updates = []
        def progress_callback(progress, message):
            progress_updates.append((progress, message))
            print(f"进度: {progress}% - {message}")
        
        start_time = time.time()
        violations = parser.parse_log_file_streaming(file_path, progress_callback)
        parse_time = time.time() - start_time
        
        print(f"✅ 高性能解析器测试结果:")
        print(f"   违例数量: {len(violations):,}")
        print(f"   解析时间: {parse_time:.2f} 秒")
        print(f"   处理速度: {len(violations)/parse_time:.0f} 记录/秒")
        print(f"   进度更新: {len(progress_updates)} 次")
        
        return True, parse_time, len(violations)
        
    except Exception as e:
        print(f"❌ 高性能解析器测试失败: {e}")
        return False, 0, 0


def main():
    """主测试函数"""
    print("Timing Violation 解析器修复验证")
    print("专门测试6万行大文件解析性能")
    print("=" * 50)
    
    # 创建临时测试文件
    test_file = "large_test_vio.log"
    
    try:
        # 生成6万条违例的测试文件
        generate_large_test_file(test_file, 60000)
        
        # 测试标准解析器
        std_success, std_time, std_count = test_standard_parser(test_file)
        
        # 测试高性能解析器
        hp_success, hp_time, hp_count = test_high_performance_parser(test_file)
        
        # 对比结果
        print(f"\n{'='*50}")
        print("性能对比结果:")
        print(f"{'解析器':<15} {'成功':<6} {'时间(秒)':<10} {'违例数':<8} {'速度(记录/秒)'}")
        print("-" * 50)
        
        if std_success:
            std_speed = std_count / std_time if std_time > 0 else 0
            print(f"{'标准解析器':<15} {'✅':<6} {std_time:<10.2f} {std_count:<8} {std_speed:<.0f}")
        else:
            print(f"{'标准解析器':<15} {'❌':<6} {'N/A':<10} {'N/A':<8} {'N/A'}")
        
        if hp_success:
            hp_speed = hp_count / hp_time if hp_time > 0 else 0
            print(f"{'高性能解析器':<15} {'✅':<6} {hp_time:<10.2f} {hp_count:<8} {hp_speed:<.0f}")
        else:
            print(f"{'高性能解析器':<15} {'❌':<6} {'N/A':<10} {'N/A':<8} {'N/A'}")
        
        # 性能提升计算
        if std_success and hp_success and std_time > 0 and hp_time > 0:
            speedup = std_time / hp_time
            print(f"\n🚀 性能提升: {speedup:.2f}x")
            
            if hp_time < 10:
                print("✅ 解析速度优秀，GUI不会卡死")
            elif hp_time < 30:
                print("⚠️  解析速度良好，但仍有优化空间")
            else:
                print("❌ 解析速度仍然较慢，需要进一步优化")
        
        # 修复验证
        print(f"\n{'='*50}")
        print("修复验证结果:")
        
        if hp_success and hp_time < 15:
            print("✅ GUI卡死问题已修复 - 解析时间在可接受范围内")
        else:
            print("❌ GUI卡死问题未完全修复 - 解析时间仍然过长")
        
        if std_success or hp_success:
            print("✅ 程序崩溃问题已修复 - 解析器能正常完成")
        else:
            print("❌ 程序崩溃问题未修复 - 解析器仍然失败")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\n🧹 已清理测试文件: {test_file}")
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    main()
