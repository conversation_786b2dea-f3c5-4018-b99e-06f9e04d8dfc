#!/usr/bin/env python3
"""
测试高性能表格分页功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel

# 添加插件路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_window import HighPerformanceTableView

def create_test_data(count=1000):
    """创建测试数据"""
    violations = []
    for i in range(count):
        violation = {
            'id': i + 1,
            'num': i + 1,
            'hier': f'tb.cpu_top.core{i % 4}.pipeline.stage{i % 8}.unit_{i}',
            'time_fs': 1000000 + (i * 1000),
            'check_info': f'Setup time violation on signal data_{i % 16}[{i % 32}]',
            'status': 'pending' if i % 3 == 0 else ('confirmed' if i % 3 == 1 else 'ignored'),
            'confirmer': f'user{i % 5}' if i % 3 != 0 else '',
            'result': 'pass' if i % 4 == 0 else ('issue' if i % 4 == 1 else ''),
        }
        violations.append(violation)
    return violations

class PaginationTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("高性能表格分页测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("分页功能测试 - 每页显示200条记录，总共1000条记录")
        info_label.setStyleSheet("font-size: 14px; font-weight: bold; padding: 10px; background-color: #e8f4fd;")
        layout.addWidget(info_label)
        
        # 创建高性能表格
        self.table = HighPerformanceTableView(self)
        layout.addWidget(self.table)
        
        # 创建测试数据并更新表格
        test_data = create_test_data(1000)  # 创建1000条测试数据
        print(f"创建了 {len(test_data)} 条测试数据")
        
        self.table.update_data(test_data)
        print("表格数据更新完成")
        
        # 连接信号用于测试
        self.table.cell_double_clicked.connect(self.on_cell_double_clicked)
        self.table.action_button_clicked.connect(self.on_action_clicked)
    
    def on_cell_double_clicked(self, row, col):
        """测试双击事件"""
        print(f"双击事件: 行={row}, 列={col}")
    
    def on_action_clicked(self, row, action):
        """测试操作按钮点击"""
        print(f"操作按钮点击: 行={row}, 动作={action}")

def main():
    app = QApplication(sys.argv)
    
    window = PaginationTestWindow()
    window.show()
    
    print("分页测试窗口已显示")
    print("测试要点:")
    print("1. 检查是否显示分页控件（首页、上一页、下一页、末页按钮）")
    print("2. 检查页码信息是否正确（第1页，共5页）")
    print("3. 检查记录信息是否正确（显示1-200条，共1000条记录）")
    print("4. 测试分页按钮是否正常工作")
    print("5. 检查行高是否合适（35px）")
    print("6. 测试双击复制和操作按钮功能")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
