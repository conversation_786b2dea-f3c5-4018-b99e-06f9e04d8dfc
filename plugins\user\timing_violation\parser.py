"""
时序违例日志解析器

提供vio_summary.log文件解析功能，支持时序违例条目提取和时间单位转换。
"""

import os
import re
import time
from typing import List, Dict, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QThread


class VioLogParser(QObject):
    """时序违例日志解析器"""

    def __init__(self):
        super().__init__()
        # 预编译正则表达式以提升性能
        self.time_pattern = re.compile(r'(\d+(?:\.\d+)?)\s*([A-Z]*)')

        # 性能统计
        self.parse_stats = {
            'total_lines': 0,
            'processed_violations': 0,
            'parse_time': 0.0
        }
    
    def parse_log_file(self, file_path: str, progress_callback: Optional[Callable[[int, str], None]] = None) -> List[Dict]:
        """解析vio_summary.log文件（优化版本）

        Args:
            file_path: 日志文件路径
            progress_callback: 进度回调函数 (progress_percent, status_message)

        Returns:
            List[Dict]: 解析出的违例列表

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式错误
        """
        start_time = time.time()

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"日志文件不存在: {file_path}")

        if not file_path.endswith('vio_summary.log'):
            raise ValueError("请选择vio_summary.log文件")

        # 获取文件大小用于进度计算
        file_size = os.path.getsize(file_path)
        if progress_callback:
            progress_callback(0, f"开始解析文件 ({file_size:,} 字节)...")

        violations = []
        current_violation = {}
        processed_bytes = 0
        last_progress = 0

        # 性能优化：批量处理和进度报告
        batch_size = 1000  # 每处理1000行报告一次进度
        line_count = 0

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    processed_bytes += len(line.encode('utf-8')) + 1  # +1 for newline
                    line_count += 1

                    # 定期更新进度
                    if progress_callback and line_count % batch_size == 0:
                        progress = min(int((processed_bytes / file_size) * 85), 85)  # 最多到85%，留15%给后处理
                        if progress > last_progress:
                            progress_callback(progress, f"解析中... 已处理 {line_count:,} 行，找到 {len(violations):,} 条违例")
                            last_progress = progress

                    # 跳过空行
                    if not line:
                        continue

                    # 检测分隔线，表示一个违例条目的结束
                    if line.startswith('----'):
                        if current_violation:
                            # 验证违例条目的完整性
                            if self._validate_violation(current_violation):
                                # 转换时间单位并添加到列表
                                processed_violation = self._process_violation(current_violation)
                                violations.append(processed_violation)
                            else:
                                print(f"警告: 第{line_num}行附近的违例条目不完整: {current_violation}")
                            current_violation = {}
                        continue

                    # 解析键值对（优化：减少字符串操作）
                    colon_pos = line.find(' : ')
                    if colon_pos != -1:
                        key = line[:colon_pos].strip()
                        value = line[colon_pos + 3:].strip()
                        current_violation[key] = value
                    else:
                        # 处理可能的多行内容
                        if current_violation and 'Check' in current_violation:
                            current_violation['Check'] += ' ' + line

                # 处理最后一个违例条目
                if current_violation:
                    if self._validate_violation(current_violation):
                        processed_violation = self._process_violation(current_violation)
                        violations.append(processed_violation)
                    else:
                        print(f"警告: 文件末尾的违例条目不完整: {current_violation}")

        except Exception as e:
            raise ValueError(f"解析文件失败: {str(e)}")

        # 最终处理
        if progress_callback:
            progress_callback(90, f"后处理中... 共找到 {len(violations):,} 条违例记录")

        if not violations:
            raise ValueError("文件中没有找到有效的时序违例记录")

        # 更新统计信息
        parse_time = time.time() - start_time
        self.parse_stats.update({
            'total_lines': line_count,
            'processed_violations': len(violations),
            'parse_time': parse_time
        })

        if progress_callback:
            progress_callback(95, f"解析完成: {len(violations):,} 条记录，耗时 {parse_time:.2f}秒")

        print(f"成功解析 {len(violations)} 条时序违例记录，耗时 {parse_time:.2f}秒")
        return violations
    
    def _validate_violation(self, violation: Dict) -> bool:
        """验证违例条目的完整性
        
        Args:
            violation: 违例条目字典
            
        Returns:
            bool: 是否有效
        """
        required_fields = ['NUM', 'Hier', 'Time', 'Check']
        return all(field in violation and violation[field] for field in required_fields)
    
    def _process_violation(self, violation: Dict) -> Dict:
        """处理违例条目，转换时间单位等
        
        Args:
            violation: 原始违例条目
            
        Returns:
            Dict: 处理后的违例条目
        """
        processed = violation.copy()
        
        # 转换NUM为整数
        try:
            processed['NUM'] = int(violation['NUM'])
        except ValueError:
            processed['NUM'] = 0
        
        # 转换时间单位
        time_str = violation['Time']
        processed['time_fs'] = self.convert_time_to_fs(time_str)
        processed['time_ns'] = self.convert_time_to_ns(time_str)
        
        return processed
    
    def convert_time_to_fs(self, time_str: str) -> int:
        """转换时间单位到飞秒（优化版本）

        Args:
            time_str: 时间字符串，如 "1523423 FS"

        Returns:
            int: 飞秒数
        """
        time_str = time_str.upper().strip()

        # 使用预编译的正则表达式
        match = self.time_pattern.match(time_str)
        if not match:
            print(f"警告: 无法解析时间格式: {time_str}")
            return 0

        value_str, unit = match.groups()
        try:
            value = float(value_str)
        except ValueError:
            print(f"警告: 无法解析时间数值: {value_str}")
            return 0

        # 转换到飞秒（优化：使用字典查找替代if-elif链）
        unit_multipliers = {
            'FS': 1,
            '': 1,
            'PS': 1000,
            'NS': 1000000
        }

        multiplier = unit_multipliers.get(unit)
        if multiplier is not None:
            return int(value * multiplier)
        else:
            print(f"警告: 未知的时间单位: {unit}")
            return int(value)  # 假设为飞秒
    
    def convert_time_to_ns(self, time_str: str) -> float:
        """转换时间单位到纳秒（优化版本）

        Args:
            time_str: 时间字符串，如 "1523423 FS"

        Returns:
            float: 纳秒数
        """
        time_str = time_str.upper().strip()

        # 使用预编译的正则表达式
        match = self.time_pattern.match(time_str)
        if not match:
            print(f"警告: 无法解析时间格式: {time_str}")
            return 0.0

        value_str, unit = match.groups()
        try:
            value = float(value_str)
        except ValueError:
            print(f"警告: 无法解析时间数值: {value_str}")
            return 0.0

        # 转换到纳秒（优化：使用字典查找）
        unit_divisors = {
            'NS': 1.0,
            '': 1.0,
            'PS': 1000.0,
            'FS': 1000000.0
        }

        divisor = unit_divisors.get(unit)
        if divisor is not None:
            return value / divisor
        else:
            print(f"警告: 未知的时间单位: {unit}")
            return value  # 假设为纳秒
    
    def format_time_display(self, time_fs: int) -> str:
        """格式化时间显示
        
        Args:
            time_fs: 飞秒数
            
        Returns:
            str: 格式化的时间字符串
        """
        if time_fs >= 1000000:
            # 显示为纳秒
            ns = time_fs / 1000000
            return f"{ns:.3f} ns"
        elif time_fs >= 1000:
            # 显示为皮秒
            ps = time_fs / 1000
            return f"{ps:.3f} ps"
        else:
            # 显示为飞秒
            return f"{time_fs} fs"


class AsyncVioLogParser(QThread):
    """异步时序违例日志解析器（优化版本）"""

    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度百分比, 状态信息
    parsing_completed = pyqtSignal(list)     # 解析完成，返回违例列表
    parsing_failed = pyqtSignal(str)         # 解析失败，返回错误信息

    def __init__(self, file_path: str):
        super().__init__()
        self.file_path = file_path
        self.parser = VioLogParser()
        self._is_cancelled = False

    def cancel(self):
        """取消解析"""
        self._is_cancelled = True

    def run(self):
        """异步解析线程主函数（优化版本）"""
        try:
            start_time = time.time()
            self.progress_updated.emit(0, "初始化解析器...")

            # 检查文件
            if not os.path.exists(self.file_path):
                self.parsing_failed.emit(f"文件不存在: {self.file_path}")
                return

            # 获取文件信息
            file_size = os.path.getsize(self.file_path)
            self.progress_updated.emit(5, f"验证文件格式... (文件大小: {file_size:,} 字节)")

            if self._is_cancelled:
                return

            # 创建进度回调函数
            def progress_callback(progress: int, message: str):
                if not self._is_cancelled:
                    # 将解析进度映射到5%-95%范围
                    mapped_progress = 5 + int(progress * 0.9)
                    self.progress_updated.emit(mapped_progress, message)

            # 解析文件（带进度回调）
            violations = self.parser.parse_log_file(self.file_path, progress_callback)

            if self._is_cancelled:
                return

            # 最终处理
            parse_time = time.time() - start_time
            self.progress_updated.emit(98, f"准备返回结果... ({len(violations):,} 条记录)")

            # 发送结果
            self.parsing_completed.emit(violations)
            self.progress_updated.emit(100, f"解析完成! 共 {len(violations):,} 条记录，耗时 {parse_time:.2f}秒")

        except Exception as e:
            error_msg = str(e)
            print(f"解析失败: {error_msg}")
            import traceback
            traceback.print_exc()
            self.parsing_failed.emit(error_msg)


class HighPerformanceVioLogParser(QObject):
    """高性能时序违例日志解析器

    专门用于处理超大文件（>50MB或>50000行），采用流式处理和内存优化策略
    """

    def __init__(self):
        super().__init__()
        # 预编译正则表达式
        self.time_pattern = re.compile(r'(\d+(?:\.\d+)?)\s*([A-Z]*)')

        # 性能配置
        self.chunk_size = 8192  # 读取块大小
        self.progress_interval = 5000  # 进度报告间隔（行数）

    def parse_log_file_streaming(self, file_path: str, progress_callback: Optional[Callable[[int, str], None]] = None) -> List[Dict]:
        """流式解析大文件

        Args:
            file_path: 日志文件路径
            progress_callback: 进度回调函数

        Returns:
            List[Dict]: 解析出的违例列表
        """
        start_time = time.time()

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"日志文件不存在: {file_path}")

        file_size = os.path.getsize(file_path)
        if progress_callback:
            progress_callback(0, f"开始流式解析大文件 ({file_size:,} 字节)...")

        violations = []
        current_violation = {}
        processed_bytes = 0
        line_count = 0
        last_progress = 0

        # 缓冲区处理
        buffer = ""

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                while True:
                    # 读取数据块
                    chunk = f.read(self.chunk_size)
                    if not chunk:
                        break

                    processed_bytes += len(chunk.encode('utf-8'))
                    buffer += chunk

                    # 处理完整的行
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()
                        line_count += 1

                        # 定期更新进度
                        if progress_callback and line_count % self.progress_interval == 0:
                            progress = min(int((processed_bytes / file_size) * 85), 85)
                            if progress > last_progress:
                                progress_callback(progress, f"流式解析中... {line_count:,} 行，{len(violations):,} 条违例")
                                last_progress = progress

                        # 处理行内容
                        self._process_line(line, line_count, current_violation, violations)

                # 处理缓冲区中剩余的内容
                if buffer.strip():
                    line_count += 1
                    self._process_line(buffer.strip(), line_count, current_violation, violations)

                # 处理最后一个违例条目
                if current_violation:
                    if self._validate_violation(current_violation):
                        processed_violation = self._process_violation(current_violation)
                        violations.append(processed_violation)

        except Exception as e:
            raise ValueError(f"流式解析失败: {str(e)}")

        parse_time = time.time() - start_time
        if progress_callback:
            progress_callback(95, f"流式解析完成: {len(violations):,} 条记录，耗时 {parse_time:.2f}秒")

        print(f"流式解析完成: {len(violations)} 条违例记录，处理 {line_count:,} 行，耗时 {parse_time:.2f}秒")
        return violations

    def _process_line(self, line: str, line_num: int, current_violation: Dict, violations: List[Dict]):
        """处理单行内容"""
        if not line:
            return

        # 检测分隔线
        if line.startswith('----'):
            if current_violation:
                if self._validate_violation(current_violation):
                    processed_violation = self._process_violation(current_violation)
                    violations.append(processed_violation)
                current_violation.clear()
            return

        # 解析键值对
        colon_pos = line.find(' : ')
        if colon_pos != -1:
            key = line[:colon_pos].strip()
            value = line[colon_pos + 3:].strip()
            current_violation[key] = value
        else:
            # 处理多行内容
            if current_violation and 'Check' in current_violation:
                current_violation['Check'] += ' ' + line

    def _validate_violation(self, violation: Dict) -> bool:
        """验证违例条目的完整性"""
        required_fields = ['NUM', 'Hier', 'Time', 'Check']
        return all(field in violation and violation[field] for field in required_fields)

    def _process_violation(self, violation: Dict) -> Dict:
        """处理违例条目"""
        processed = violation.copy()

        # 转换NUM为整数
        try:
            processed['NUM'] = int(violation['NUM'])
        except ValueError:
            processed['NUM'] = 0

        # 转换时间单位
        time_str = violation['Time']
        processed['time_fs'] = self._convert_time_to_fs(time_str)
        processed['time_ns'] = self._convert_time_to_ns(time_str)

        return processed

    def _convert_time_to_fs(self, time_str: str) -> int:
        """转换时间单位到飞秒"""
        time_str = time_str.upper().strip()
        match = self.time_pattern.match(time_str)
        if not match:
            return 0

        value_str, unit = match.groups()
        try:
            value = float(value_str)
        except ValueError:
            return 0

        unit_multipliers = {'FS': 1, '': 1, 'PS': 1000, 'NS': 1000000}
        return int(value * unit_multipliers.get(unit, 1))

    def _convert_time_to_ns(self, time_str: str) -> float:
        """转换时间单位到纳秒"""
        time_str = time_str.upper().strip()
        match = self.time_pattern.match(time_str)
        if not match:
            return 0.0

        value_str, unit = match.groups()
        try:
            value = float(value_str)
        except ValueError:
            return 0.0

        unit_divisors = {'NS': 1.0, '': 1.0, 'PS': 1000.0, 'FS': 1000000.0}
        return value / unit_divisors.get(unit, 1.0)


class HighPerformanceAsyncParser(QThread):
    """高性能异步解析器

    专门用于处理超大文件，结合了异步处理和流式解析
    """

    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度百分比, 状态信息
    parsing_completed = pyqtSignal(list)     # 解析完成，返回违例列表
    parsing_failed = pyqtSignal(str)         # 解析失败，返回错误信息

    def __init__(self, file_path: str):
        super().__init__()
        self.file_path = file_path
        self.parser = HighPerformanceVioLogParser()
        self._is_cancelled = False

    def cancel(self):
        """取消解析"""
        self._is_cancelled = True

    def run(self):
        """高性能异步解析线程主函数"""
        try:
            start_time = time.time()
            file_size = os.path.getsize(self.file_path)
            file_size_mb = file_size / (1024 * 1024)

            self.progress_updated.emit(0, f"初始化高性能解析器... (文件: {file_size_mb:.1f}MB)")

            # 检查文件
            if not os.path.exists(self.file_path):
                self.parsing_failed.emit(f"文件不存在: {self.file_path}")
                return

            if self._is_cancelled:
                return

            # 创建进度回调函数
            def progress_callback(progress: int, message: str):
                if not self._is_cancelled:
                    # 将解析进度映射到5%-95%范围
                    mapped_progress = 5 + int(progress * 0.9)
                    self.progress_updated.emit(mapped_progress, f"[高性能模式] {message}")

            # 使用流式解析
            violations = self.parser.parse_log_file_streaming(self.file_path, progress_callback)

            if self._is_cancelled:
                return

            # 最终处理
            parse_time = time.time() - start_time
            throughput = len(violations) / parse_time if parse_time > 0 else 0

            self.progress_updated.emit(98, f"准备返回结果... (吞吐量: {throughput:.0f} 记录/秒)")

            # 发送结果
            self.parsing_completed.emit(violations)
            self.progress_updated.emit(100, f"高性能解析完成! {len(violations):,} 条记录，{parse_time:.2f}秒")

        except Exception as e:
            error_msg = str(e)
            print(f"高性能解析失败: {error_msg}")
            import traceback
            traceback.print_exc()
            self.parsing_failed.emit(f"高性能解析失败: {error_msg}")


class CaseInfoParser:
    """用例信息解析器"""
    
    # 支持的corner列表
    VALID_CORNERS = [
        'npg_f1_ssg', 'npg_f2_ssg', 'npg_f3_ssg', 'npg_f4_ssg', 'npg_f5_ssg', 'npg_f6_ssg', 'npg_f7_ssg',
        'npg_f1_ffg', 'npg_f2_ffg', 'npg_f3_ffg', 'npg_f4_ffg', 'npg_f5_ffg', 'npg_f6_ffg', 'npg_f7_ffg',
        'npg_f1_tt', 'npg_f2_tt', 'npg_f3_tt'
    ]
    
    @staticmethod
    def parse_directory_name(dir_path: str) -> Dict[str, Optional[str]]:
        """从目录路径解析用例信息

        支持以下格式：
        1. {case_name}_{corner} - 如: test_case_npg_f1_ssg
        2. {case_name}_{corner}_xxx - 如: test_case_npg_f1_ffg_cloud
        3. {case_name} - 如: test_case (无corner信息)

        Args:
            dir_path: 目录路径

        Returns:
            Dict: 包含case_name和corner的字典
        """
        dir_name = os.path.basename(dir_path.rstrip(os.sep))
        print(f"解析目录名: {dir_name}")

        # 按优先级尝试匹配corner（从长到短，避免短corner被长corner包含的问题）
        sorted_corners = sorted(CaseInfoParser.VALID_CORNERS, key=len, reverse=True)

        for corner in sorted_corners:
            corner_pattern = f'_{corner}'

            # 方式1: 检查是否以 _{corner} 结尾 (格式: {case_name}_{corner})
            if dir_name.endswith(corner_pattern):
                case_name = dir_name[:-len(corner_pattern)]
                print(f"匹配格式1 - case_name: {case_name}, corner: {corner}")
                return {
                    'case_name': case_name,
                    'corner': corner
                }

            # 方式2: 检查是否包含 _{corner}_ (格式: {case_name}_{corner}_xxx)
            corner_pattern_with_suffix = f'_{corner}_'
            if corner_pattern_with_suffix in dir_name:
                # 找到corner的位置
                corner_pos = dir_name.find(corner_pattern_with_suffix)
                case_name = dir_name[:corner_pos]
                print(f"匹配格式2 - case_name: {case_name}, corner: {corner}")
                return {
                    'case_name': case_name,
                    'corner': corner
                }

        # 如果没有匹配到corner，返回整个目录名作为case_name
        print(f"未匹配到corner - case_name: {dir_name}, corner: None")
        return {
            'case_name': dir_name,
            'corner': None
        }
    
    @staticmethod
    def get_valid_corners() -> List[str]:
        """获取有效的corner列表
        
        Returns:
            List[str]: corner列表
        """
        return CaseInfoParser.VALID_CORNERS.copy()
