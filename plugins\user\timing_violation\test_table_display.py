#!/usr/bin/env python3
"""
测试高性能表格显示问题
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget

# 添加插件路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_window import HighPerformanceTableView, ViolationTableModel

def create_test_data(count=100):
    """创建测试数据"""
    violations = []
    for i in range(count):
        violation = {
            'id': i + 1,
            'num': i + 1,
            'hier': f'tb.cpu_top.core{i % 4}.pipeline.stage{i % 8}.unit_{i}',
            'time_fs': 1000000 + (i * 1000),
            'check_info': f'Setup time violation on signal data_{i % 16}[{i % 32}]',
            'status': 'pending' if i % 3 == 0 else ('confirmed' if i % 3 == 1 else 'ignored'),
            'confirmer': f'user{i % 5}' if i % 3 != 0 else '',
            'result': 'pass' if i % 4 == 0 else ('issue' if i % 4 == 1 else ''),
        }
        violations.append(violation)
    return violations

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("高性能表格测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建高性能表格
        self.table = HighPerformanceTableView(self)
        layout.addWidget(self.table)
        
        # 创建测试数据并更新表格
        test_data = create_test_data(1000)  # 创建1000条测试数据
        print(f"创建了 {len(test_data)} 条测试数据")
        
        self.table.update_data(test_data)
        print("表格数据更新完成")

def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("测试窗口已显示，请检查表格是否正确显示多行数据")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
